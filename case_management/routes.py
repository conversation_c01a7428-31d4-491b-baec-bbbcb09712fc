try:
    from flask import Blueprint, request, render_template, jsonify, session, redirect, url_for, current_app, send_from_directory, Response
    from flask_mail import Message
    import logging
    from repositories.client import ClientRepository
    from repositories.support_repository import SupportRepository
    from repositories.attorney import AttorneyRepository
    from repositories.time_entry_repository import TimeEntryRepository
    from repositories.case import CaseRepository
    from repositories.message import MessageRepository
    from repositories.document import DocumentRepository
    from repositories.case_deadline_repository import CaseDeadlineRepository
    from repositories.employee import EmployeeRepository
    from repositories.lawFirm import LawFirmRepository
    from services.conflict_checker import ConflictChecker
    from repositories.activity_log import ActivityLogRepository
    from repositories.support_repository import SupportRepository
    from repositories.case_task_repository import CaseTaskRepository
    from repositories.invoice_repository import InvoiceRepository
    from repositories.expenses_repository import ExpensesRepository
    from repositories.external_contact import ExternalContactRepository
    from repositories.case_contact_assignment import CaseContactAssignmentRepository
    from repositories.BillingInfo import BillingInfoRepository
    from repositories.CheckoutSessions import CheckoutSessionRepository
    from repositories.notification_repository import NotificationRepository
    from repositories.trust_deposit import TrustDepositRepository
    from case_management.case_validation import validate_case_data
    from case_management.file_validation import validate_file
    from case_management.extensions import db
    from case_management.extensions import mail
    import json
    import uuid
    import re
    import os
    from datetime import datetime, timezone, timedelta, date
    from werkzeug.security import generate_password_hash 
    from werkzeug.utils import secure_filename
    import random
    import string
    from dateutil.parser import parse, ParserError  # Import dateutil for flexible date parsing
    from user_management.authorization import login_required, roles_required, write_permission_required, attorney_required, employee_permission_required, permission_required  # adjust import as needed
    from user_management.roles import has_role
    import traceback
    from case_management.models import CustomDocumentType, Employee, Client, CaseDeadline, CaseTask, BillingInfo, TrustDeposit, Attorney, TimeEntry, Admin,SupportTicket, Attorney, LawFirm,Feedback, ConflictCheckLog, DocumentAuditLog # Import your models 
    from decimal import Decimal, ROUND_HALF_UP
    #import 
    from utils.shared import validators 
    from utils.shared.pagination import Paginator
    from utils.filters import filters as util_filters
    #import serializers
    from serializers.invoice_serializer import serialize_invoice
    from serializers.expense_serializer import serialize_expense
    from utils.generate_pdf import generate_invoice_pdf
    from utils.generate_mail import send_invoice_email
    from utils.get_payout_data import get_user_payout_data
    from utils.get_payout_summary import calculate_payout_summary
    from utils.time_tracker import get_user_display_info , get_current_user_info
    from utils.audit_logger import log_document_action

    import io
    import csv
    import stripe
    from utils.get_payout_data import get_user_payout_data
    from utils.get_payout_summary import calculate_payout_summary
    import io
    import csv
  # add this at the top if not already imported

    from utils.helpers import safe_iso

except Exception as e:
    print(f"Import error in routes.py: {e}")


logging.basicConfig(level=logging.INFO)

# Create a blueprint for the routes
routes = Blueprint('routes', __name__)

# ---------- FORM RENDERING ROUTES ----------
# Define a test route
print("Adding route: /test-route")
@routes.route('/test-route', methods=['GET'])
def test_route():
    return jsonify({"message": "Test route works!"})

@routes.route('/update-client-form', methods=['GET'])
#@attorney_required
def update_client_form():
    return render_template('update_client_form.html')

@routes.route('/update-case-form', methods=['GET'])
def render_update_case_form():
    """Render the update case form."""
    return render_template('update_case.html')


@routes.route('/add_case', methods=['GET'])
def add_case_form():
    if 'username' not in session or session.get('user_role') != 'attorney':
        return redirect(url_for('auth.home'))
    return render_template('add_new_case_form.html')

@routes.route('/upload-form', methods=['GET'])
def display_upload_form():
    # Check if the user is logged in and has a valid role (attorney or client)
    #if 'username' not in session or session.get('user_role') not in ['attorney', 'client']:
    #    return redirect(url_for('auth.home'))

    # Determine the user's role and ID
    user_role = session.get('user_role')
    user_id = session.get('attorney_id') if user_role == 'attorney' else session.get('client_id')

    # Fetch the client's name if the user is a client
    client_name = None
    if user_role == 'client':
        client = ClientRepository.get_client_by_id(user_id)  # Use the repository method
        if client:
            client_name = client.name  # Get the client's name

    # Pass the user's role, ID, and name (if applicable) to the template
    return render_template('upload_file_form.html', user_role=user_role, user_id=user_id, client_name=client_name)

@routes.route('/dashboard', methods=['GET'])
def case_dashboard():
    return render_template('case_dashboard.html')

@routes.route('/messaging-ui', methods=['GET'])
def render_messaging_ui():
    if 'username' not in session or session.get('user_role') != 'attorney':
        return redirect(url_for('auth.home'))
    return render_template('messaging_ui.html')

@routes.route('/client/messaging/form', methods=['GET'])
def client_message_form():
    """Render the client messaging form for the logged-in client."""
    if 'username' not in session or session.get('user_role') != 'client':
        return redirect(url_for('auth.home'))
    return render_template('client_message_form.html')

@routes.route('/add-client-form', methods=['GET'])
def add_client_form():
    if 'username' not in session or session.get('user_role') != 'attorney':
        return redirect(url_for('auth.home'))
    return render_template('add_client_form.html')  # This serves the HTML form

@routes.route('/add-contact-form', methods=['GET'])
def add_contact_form():
    if 'username' not in session or session.get('user_role') != 'attorney':
        return redirect(url_for('auth.home'))
    return render_template('add_contact_form.html')  # This serves the HTML form

@routes.route('/delete_client')
def index():
    return render_template('delete_client.html')


@routes.route('/delete_case_form')
def delete_case_form():
    """Renders the form to delete a case."""
    return render_template('delete_case.html')

@routes.route('/log-activity-page', methods=['GET'])
def log_activity_page():
    """
    Renders the activity_template.html page for manual or timer-based activity logging.
    """
    return render_template('activity_template.html')


@routes.route('/update-firm-attys-and-employees', methods=['GET'])
def update_firm_attys_and_employees():
    """
    Route to render the update_firm_attys_and_employees.html template.
    """
    return render_template('update_firm_attys_and_employees.html')

# ---------- API ROUTES ----------
# ---------- API "GET" ROUTES ----------

@routes.route('/api/get-user-data', methods=['GET'])
def get_user_data():
    # Check if the user is logged in
    if 'username' not in session or session.get('user_role') not in ['attorney', 'client']:
        return jsonify({'error': 'Unauthorized'}), 401

    # Determine the user's role and ID
    user_role = session.get('user_role')
    user_id = session.get('attorney_id') if user_role == 'attorney' else session.get('client_id')

    # Fetch the client's name if the user is a client
    client_name = None
    if user_role == 'client':
        client = ClientRepository.get_client_by_id(user_id)
        if client:
            client_name = client.name

    return jsonify({
        'userRole': user_role,
        'userId': user_id,
        'clientName': client_name
    }), 200

@routes.route('/test')
def test():
      return 'Test route working'

@routes.route('/__ping')
def ping():
    return "pong"



@routes.route('/attorney/clients', methods=['GET'])
def get_attorney_clients():
    try:
        print("Route /attorney/clients was called.")  # Debugging line
        if not has_role('attorney'):
            return jsonify({"error": "Access denied. Attorneys only."}), 403

        attorney_id = session.get('attorney_id')
        print(f"Session Attorney ID: {attorney_id}")  # Debugging line

        if not attorney_id:
            return jsonify({"error": "Attorney not found in session."}), 404
        
    # Fetch clients where the attorney is the primary attorney
        primary_clients = ClientRepository.get_clients_by_primary_attorney(attorney_id)

        # Fetch clients where the attorney is a collaborator
        collaborating_clients = ClientRepository.get_clients_by_collaborating_attorney(attorney_id)

        # Combine the two lists and remove duplicates
        all_clients = list(set(primary_clients + collaborating_clients))

        # Format the clients for the response
        clients_data = [
            {
                "client_id": client.client_id,
                "name": client.name,
                "email": client.email,
                "phone_number": client.phone_number,
                "address": client.address,
                "law_firm_id": client.law_firm_id,
                "custom_fields": client.custom_fields
            }
            for client in all_clients
        ]
        return jsonify(clients_data)
    except Exception as e:
        print(f"Error fetching clients: {str(e)}")
        return jsonify({"error": "An unexpected error occurred."}), 500


@routes.route('/client/<string:client_id>/cases', methods=['GET'])
def obtain_client_cases(client_id):
    if not has_role('attorney') and not has_role('client'):
        return jsonify({"error": "Access denied."}), 403
    
    

    cases = CaseRepository.get_cases_by_client_id(client_id)
    cases_data = [{
        "case_id": case.case_id,
        "case_name": case.case_name
    } for case in cases]
    return jsonify(cases_data)


@routes.route('/case/<string:case_id>/details', methods=['GET'])
def get_all_case_details(case_id):
    if not has_role('attorney') and not has_role('client'):
        return jsonify({"error": "Access denied."}), 403

    try:
        # Get the current user's role from the session
        user_role = session.get('user_role')  # Retrieve role from session
        username = session.get('username')  # Retrieve username from session

        case = CaseRepository.get_case_by_id(case_id)
        if not case:
            return jsonify({"error": "Case not found"}), 404

        deadlines = CaseDeadlineRepository.get_deadlines_by_case_id(case_id) or []
        tasks = CaseTaskRepository.get_tasks_by_case_id(case_id) or []
        documents = DocumentRepository.get_documents_by_case_id(case_id) or []
        messages = MessageRepository.get_messages_by_case_id(case_id) or []

        def parse_date(date):
            try:
                if isinstance(date, str):
                    return parse(date).isoformat()
                elif isinstance(date, datetime):
                    return date.isoformat()
                else:
                    return None
            except Exception as e:
                print(f"Error parsing date: {e}")
                return None

        case_data = {
            "case_id": case.case_id,
            "case_name": case.case_name,
            "case_type": case.case_type,
            "case_status": case.case_status,
            "description": case.description,
            "custom_fields": case.custom_fields,
            "client_name": case.client_name,
            "username": username,
            "user_role": user_role,  # Include the user's role from the session
            "deadlines": [{
                "deadline_date": parse_date(deadline.deadline_date),
                "deadline_type": deadline.deadline_type,
                "notes": deadline.notes
            } for deadline in deadlines],
            "tasks": [{
                "task_type": task.task_type,
                "description": task.description,
                "due_date": parse_date(task.due_date),
                "status": task.status
            } for task in tasks],
            "documents": [{
                "id": doc.id,
                "filename": doc.filename,
                "original_name": doc.original_name,
                "uploaded_at": parse_date(doc.uploaded_at)
            } for doc in documents],
            "messages": [{
                "sender": msg.sender,
                "content": msg.content,
                "timestamp": parse_date(msg.timestamp)
            } for msg in messages]
        }
        
        # 👇 Get contact assignments with names
        assignment_results = CaseContactAssignmentRepository.get_assignments_by_case(case_id)
        case_data["assignments"] = [{
            "assignment_id": a.id,
            "external_contact_id": a.external_contact_id,
            "role": a.role,
            "assigned_date": parse_date(a.assigned_date),
            "external_contact_name": name
        } for a, name in assignment_results]

        
        return render_template('case_detail.html', case=case_data)

    except Exception as e:
        print(f"Error fetching case details for case_id {case_id}: {e}")
        traceback.print_exc()
        return jsonify({"error": "Internal server error"}), 500
    

@routes.route('/api/search-client', methods=['GET', 'POST'])
def search_client():
    try:
        # Handle JSON or form data
        if request.method == 'POST' and request.is_json:
            data = request.get_json(silent=True)
            query = data.get('query', '').strip()
        else:
            query = request.args.get('query', '').strip()

        if not query:
            return jsonify([]), 200

        # Use a repository method to search clients by name
        clients = ClientRepository.search_clients_by_name(query)
        client_list = [{
            "client_id": client.client_id,
            "name": client.name,
            "username": client.username,
            "email": client.email,
            "phone_number": client.phone_number,
            "address": client.address,
            "description": client.description,
            "custom_fields": client.custom_fields

        } for client in clients]

        return jsonify(client_list), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/search-cases', methods=['GET', 'POST'])
def search_cases():
    try:
        # Handle JSON or form data
        if request.method == 'POST' and request.is_json:
            data = request.get_json(silent=True)
        else:
            data = request.args

        if not data:
            return jsonify({"error": "No data provided"}), 400

        client_name = data.get('client_name')
        case_name = data.get('case_name')

        if client_name:
            cases = CaseRepository.get_cases_by_client_name(client_name)
        elif case_name:
            cases = CaseRepository.get_cases_by_case_name(case_name)
        else:
            return jsonify({"error": "You must provide either a client_name or case_name"}), 400

        if not cases:
            return jsonify({"error": "No cases found"}), 404

        # Format cases with deadlines and tasks
        case_list = []
        for case in cases:
            case_data = {
                "case_id": case.case_id,
                "client_name": case.client_name,
                "case_name": case.case_name,
                "case_type": case.case_type,
                "case_status": case.case_status,
                "description": case.description,
                "deadlines": [{
                    "deadline_id": deadline.id,
                    "deadline_date": deadline.deadline_date.isoformat() if deadline.deadline_date else None,
                    "deadline_type": deadline.deadline_type,
                    "notes": deadline.notes
                } for deadline in case.deadlines],
                "tasks": [{
                    "task_id": task.id,
                    "task_type": task.task_type,
                    "description": task.description,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "status": task.status
                } for task in case.tasks]
            }
            case_list.append(case_data)

        return jsonify({"cases": case_list}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@routes.route('/api/get-clients', methods=['GET'])
def get_clients():
    try:
        clients = ClientRepository.get_all_clients()
        client_list = [{"id": client.client_id, "name": client.name} for client in clients]
        return jsonify(client_list)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@routes.route('/api/get-case-details/<case_id>', methods=['GET'])
def get_case_details(case_id):
    try:
        case = CaseRepository.get_case_by_id(case_id)
        if not case:
            return jsonify({"error": "Case not found"}), 404

        case_details = {
            "case_id": case.case_id,
            "client_id": case.client_id,
            "client_name": case.client_name,
            "case_name": case.case_name,
            "case_type": case.case_type,
            "case_status": case.case_status,
            "description": case.description
        }
        return jsonify(case_details), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
@routes.route('/api/get-cases-by-client/<client_name>', methods=['GET'])
def get_cases_by_client_name(client_name):
    """Retrieve cases for a client by name, only accessible by attorneys."""
    #if 'username' not in session or session.get('user_role') != 'attorney':
     #   return jsonify({"error": "Unauthorized"}), 403  # Restrict access to attorneys only

    try:
        # Retrieve the client by name
        client = ClientRepository.get_client_by_name(client_name)
        if not client:
            print(f"No client found with name: {client_name}")
            return jsonify({"error": "Client not found"}), 404
        
        print(f"Client found: ID={client.client_id}, Name={client.name}")


        # Retrieve cases for the client
        cases = CaseRepository.get_cases_by_client(client.client_id)
        if not cases:
            print(f"No cases found for client ID: {client.client_id}")
            return jsonify({"error": "No cases found for this client"}), 404
        
         # Print the retrieved cases
        print(f"Retrieved cases for client ID {client.client_id}:")
        for case in cases:
            print(f"Case ID: {case.case_id}, Case Name: {case.case_name}")

        # Format cases with deadlines and tasks
        case_list = []
        for case in cases:
            case_data = {
                "case_id": case.case_id,
                "case_name": case.case_name,
                "case_type": case.case_type,
                "case_status": case.case_status,
                "description": case.description,
                "deadlines": [{
                    "deadline_id": deadline.id,
                    "deadline_date": deadline.deadline_date.isoformat() if deadline.deadline_date else None,
                    "deadline_type": deadline.deadline_type,
                    "notes": deadline.notes
                } for deadline in case.deadlines],
                "tasks": [{
                    "task_id": task.id,
                    "task_type": task.task_type,
                    "description": task.description,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "status": task.status
                } for task in case.tasks]
            }
            case_list.append(case_data)

        return jsonify({"cases": case_list}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    

@routes.route('/get-cases/<string:client_id>', methods=['GET'], endpoint='get_cases_by_client_id')
def get_cases_by_client_id(client_id):
    try:
        # Fetch cases for the client
        cases = CaseRepository.get_cases_by_client(client_id)
        case_list = [{"case_id": c.case_id, "title": c.title} for c in cases]
        return {"status": "success", "cases": case_list}

    except Exception as e:
        return {"status": "error", "message": f"An error occurred: {str(e)}"}, 500



@routes.route('/api/get-cases/<username>', methods=['GET'], endpoint='get_cases_by_username')
def get_cases_by_username(username):
    if 'user_role' not in session or session['user_role'] != 'admin':
        return jsonify({"error": "Unauthorized"}), 403

    client = ClientRepository.get_client_by_username(username)
    if not client:
        return jsonify([])

    cases = CaseRepository.get_cases_by_client(client.client_id)
    case_list = [{"id": case.case_id, "case_name": case.case_name} for case in cases]
    return jsonify(case_list)

@routes.route('/api/client_cases', methods=['GET'])
def get_client_cases():
    client_id = session.get('client_id')
    if not client_id:
        return jsonify({"error": "Unauthorized"}), 401
    cases = CaseRepository.get_cases_by_client(client_id)
    return jsonify({"cases": [{"id": case.case_id, "name": case.case_name} for case in cases]})

@routes.route('/api/get-all-cases', methods=['GET'])
def get_all_cases():
    try:
        cases = CaseRepository.get_all_cases()
        case_list = [{
            "case_id": case.case_id,
            "client_name": case.client_name,
            "case_type": case.case_type,
            "case_status": case.case_status
        } for case in cases]
        return jsonify(case_list), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@routes.route('/api/client_messages/<case_id>', methods=['GET'])
def get_client_messages(case_id):
    if 'username' not in session or session.get('user_role') != 'client':
        return jsonify({"error": "Unauthorized"}), 401

    client_id = session.get('client_id')
    case = CaseRepository.get_case_by_id(case_id)

    if not case or case.client_id != client_id:
        return jsonify({"error": "Forbidden"}), 403

    messages = MessageRepository.get_messages_by_case_id(case_id)
    return jsonify({
        "case_id": case.case_id,
        "case_name": case.case_name,
        "messages": [
            {"sender": msg.sender, "content": msg.content, "timestamp": msg.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            for msg in messages
        ]
    })
    
@routes.route('/api/get-messages/<case_id>', methods=['GET'])
def get_messages(case_id):
    # Use the repository method to retrieve messages for the provided case_id
    messages = MessageRepository.get_messages_by_case_id(case_id)
    
    username=session.get('username')
    # Initialize an empty list to hold the converted message data
    messages_list = []
    
    # Loop through each message returned by the repository
    for message in messages:
        # Convert each message into a dictionary with relevant fields
        messages_list.append({
            'timestamp': message.timestamp.isoformat() if message.timestamp else "",  # Format the timestamp as an ISO string
            'sender': message.sender,     # Get the sender from the message
            'content': message.content, # Get the content from the message
            "username": username  # Include the username from the session
        })
    
    # Return the list of messages as a JSON response
    return jsonify(messages_list)



@routes.route('/api/get-document-types', methods=['GET'])
def get_document_types():
    # Retrieve user information from the session
    username = session.get('username')
    user_role = session.get('user_role')
    user_id = session.get('client_id') if user_role == 'client' else session.get('attorney_id')

    if not username or not user_role or not user_id:
        return jsonify({"error": "User not logged in"}), 401

    # Predefined document types
    predefined_types = [
        "Contracts",
        "Pleadings",
        "Evidence (Video)",
        "Evidence (Medical Records)",
        "Depositions",
        "Affidavit",
        "Research",
        "Discovery"
    ]

    # Fetch custom document types using the repository
    custom_type_names = DocumentRepository.get_custom_document_types(user_id)

    # Combine predefined and custom types
    all_types = predefined_types + custom_type_names

    return jsonify(all_types)



@routes.route('/get-employees', methods=['GET'])
def get_employees():
    try:
        # Retrieve the law firm ID from the session
        law_firm_id = session.get('law_firm_id')
        print(" DEBUG: Law Firm ID from session:", law_firm_id)

        if not law_firm_id:
            return {"status": "error", "message": "User not associated with a law firm."}, 400

        # Fetch employees for the law firm
        employees = EmployeeRepository.get_employees_by_law_firm(law_firm_id)
        print(" DEBUG: Employees returned:", employees)

        employee_list = [{"id": e.id, "name": e.name, "role": e.role} for e in employees]
        return {"status": "success", "employees": employee_list}

    except Exception as e:
        print("❌ ERROR:", str(e))  # Optional: print full error
        return {"status": "error", "message": f"An error occurred: {str(e)}"}, 500
    

@routes.route('/get-clients-by-employee/<string:employee_id>', methods=['GET'])
def get_clients_by_employee(employee_id):
    try:
       # Fetch the employee by ID
        print(f"Fetching employee with ID: {employee_id}")
        employee = EmployeeRepository.get_employee_by_id(employee_id)
        if not employee:
            print("Employee not found.")
            return {"status": "error", "message": "Employee not found."}, 404

        # Fetch clients for the employee's law firm
        print(f"Fetching clients for law firm ID: {employee.law_firm_id}")
        clients = ClientRepository.get_clients_by_law_firm(employee.law_firm_id)
        print(f"Found {len(clients)} clients.")
        client_list = [{"client_id": c.client_id, "name": c.name} for c in clients]
        return {"status": "success", "clients": client_list}

    except Exception as e:
        return {"status": "error", "message": f"An error occurred: {str(e)}"}, 500
    
    
    
    
@routes.route('/api/search-attorney', methods=['GET'])
def search_attorney():
    name = request.args.get('name', '')
    attorneys = AttorneyRepository.get_attorneys_by_name(name)

    results = []
    for attorney in attorneys:
        law_firm = LawFirmRepository.get_law_firm_by_id(attorney.law_firm_id)
        results.append({
            "attorney_id": attorney.attorney_id,
            "name": attorney.name,
            "specialization": attorney.specialization,
            "description": attorney.description,
            "email": attorney.email,
            "phone_number": attorney.phone_number,
            "address": attorney.address,
            "role": attorney.role,
            "hourly_rate": attorney.hourly_rate,
            "law_firm_name": law_firm.name if law_firm else None,
            "law_firm_address": law_firm.address if law_firm else None,
            "law_firm_email": law_firm.contact_email if law_firm else None,
            "law_firm_phone": law_firm.phone_number if law_firm else None,
        })

    return jsonify(results)


@routes.route('/api/search-employee', methods=['GET'])
def search_employee():
    name = request.args.get('name', '')
    employees = EmployeeRepository.get_employees_by_name(name)

    results = []
    for employee in employees:
        law_firm = LawFirmRepository.get_law_firm_by_id(employee.law_firm_id)
        results.append({
            "id": employee.id,
            "name": employee.name,
            "email": employee.email,
            "address": employee.address,
            "role": employee.role,
            "hourly_rate": employee.hourly_rate,
            "law_firm_name": law_firm.name if law_firm else None,
            "law_firm_address": law_firm.address if law_firm else None,
            "law_firm_email": law_firm.contact_email if law_firm else None,
            "law_firm_phone": law_firm.phone_number if law_firm else None,
        })

    return jsonify(results)



 # ---------- API "POST/PUT/DELETE" ROUTES -------------

@routes.route('/delete-clients-by-name/<client_name>', methods=['DELETE'])
def delete_client_by_name(client_name):
    print(f"Attempting to delete client: {client_name}")  # Log the client name
    success = ClientRepository.delete_client_by_name(client_name)
    if success:
        print(f"Client deleted successfully: {client_name}")  # Log success
        return jsonify({'success': True, 'message': 'Client deleted successfully'})
    else:
        print(f"Failed to delete client: {client_name}")  # Log failure
        return jsonify({'success': False, 'message': 'Failed to delete client'}), 200


@routes.route('/delete-clients/<client_id>', methods=['DELETE'])
def delete_client(client_id):
    success = ClientRepository.delete_client_by_id(client_id)
    return jsonify({'success': success})


@routes.route('/api/add-case', methods=['POST'])
@login_required
@roles_required('attorney', 'employee')
@employee_permission_required('create_case')
def add_case():
    try:
        context = util_filters.get_user_context()
        representative_role = context.get("user_role")
        representative_id = context.get("id")
        data = request.get_json() if request.is_json else request.form
        # Prepare case data for conflict check
        case_data = {
            'attorney_id': data.get('attorney_id'),
            'client_id': data.get('client_id'),
            'case_type': data.get('case_type'),
            'id': data.get('case_id')  # For update scenarios
        }
        
        # Retrieve law_firm_id from context or session
        law_firm_id = context.get("law_firm_id") or session.get("law_firm_id")

        # Run conflict check
        checker = ConflictChecker(db.session, law_firm_id)
        conflict_results = checker.check_conflicts(
            new_case=case_data
        )
        
        conflicts = checker.format_conflict_results(conflict_results)
        if conflicts:
            # Log the conflict check
            log_entry = ConflictCheckLog()
            log_entry.triggered_by_user_id = context.get("id")
            log_entry.triggered_by_user_type = context.get("user_role")
            log_entry.law_firm_id = law_firm_id
            log_entry.case_id_checked = data.get('case_id')
            log_entry.client_name_checked = data.get('client_name')
            log_entry.result = json.dumps({'conflicts': conflicts})
            log_entry.timestamp = datetime.utcnow()
            db.session.add(log_entry)
            db.session.commit()
            
            return jsonify({
                'warning': 'Potential conflicts detected',
                'conflicts': conflicts,
                'check_id': log_entry.id
            }), 200
            
        custom_fields = data.get('custom_fields', {})
        print("Received data:", data)  # Debugging line

        # Validate required fields first with more detailed error messages
        required_fields = {
            'client_name': 'Client Name',
            'case_name': 'Case Name', 
            'case_type': 'Case Type',
            'case_status': 'Case Status',
            'description': 'Description'
        }
        
        missing_fields = []
        field_values = {}
        for field, display_name in required_fields.items():
            value = data.get(field, '').strip()
            if not value:
                missing_fields.append(display_name)
            field_values[field] = value

        if missing_fields:
            error_msg = "Missing required fields: " + ", ".join(missing_fields)
            return jsonify({
                "error": error_msg,
                "missing_fields": list(required_fields.keys()),
                "field_errors": {f: f"{display_name} is required" 
                               for f, display_name in required_fields.items() 
                               if display_name in missing_fields}
            }), 400

        # Extract case details
        case_id = data.get('case_id') or f"CASE-{uuid.uuid4().hex[:8].upper()}"
        client_name = field_values['client_name']
        client_id = data.get('client_id', '').strip()

        # Enhanced client validation
        client = None
        if client_id:
            client = ClientRepository.get_client_by_id(client_id)
            if not client:
                return jsonify({
                    "error": f"Client ID {client_id} not found",
                    "client_error": True,
                    "invalid_client_id": True,
                    "field_errors": {
                        "client_id": "Client ID not found in system"
                    }
                }), 400
            if client.name.lower() != client_name.lower():
                return jsonify({
                    "error": f"Client name doesn't match Client ID {client_id}",
                    "client_error": True,
                    "field_errors": {
                        "client_name": f"Does not match client ID {client_id}",
                        "client_id": "Associated client name doesn't match"
                    }
                }), 400
        else:
            client = ClientRepository.get_client_by_name(client_name)
            if not client:
                session['pending_case'] = {
                    "case_id": case_id,
                    "client_name": client_name,
                    "case_name": field_values['case_name'],
                    "case_type": field_values['case_type'],
                    "case_status": field_values['case_status'],
                    "description": field_values['description'],
                    "custom_fields": custom_fields
                }
                return jsonify({
                    "error": f"Client '{client_name}' not found",
                    "client_error": True,
                    "redirect_url": url_for('routes.add_client_form'),
                    "field_errors": {
                        "client_name": "Client not found in system"
                    }
                }), 400

        # Create billing info
        billing_method = data.get("billing_method", "credit_card").lower()
        allowed_methods = {"credit_card", "ach", "crypto_payment"}

        if billing_method not in allowed_methods:
            return jsonify({
                "error": f"Invalid billing method '{billing_method}'",
                "field_errors": {
                    "billing_method": f"Must be one of: {', '.join(allowed_methods)}"
                }
            }), 400

        billing_details = data.get("billing_details", {})
        encrypted_details = json.dumps(billing_details)  # Replace with real encryption

        billing_info = BillingInfo()
        billing_info.billing_method = billing_method
        billing_info.encrypted_details = encrypted_details
        db.session.add(billing_info)
        db.session.flush()

        # Create the case
        new_case = CaseRepository.create_case(
            case_id=case_id,
            client_id=client.client_id,
            client_name=client.name,
            case_name=field_values['case_name'],
            case_type=field_values['case_type'],
            case_status=field_values['case_status'],
            description=field_values['description'],
            billing_info_id=billing_info.id,
            role=representative_role,
            representative_id=representative_id,
            custom_fields=custom_fields
        )

        # Process deadlines with better error handling
        deadlines = []
        if request.is_json:
            deadlines = data.get('deadlines', [])
        else:
            deadline_dates = request.form.getlist('deadline_date[]')
            deadline_types = request.form.getlist('deadline_type[]')
            deadline_notes = request.form.getlist('deadline_notes[]')
            deadlines = [{
                "deadline_date": d.strip(),
                "deadline_type": t.strip(),
                "notes": n.strip()
            } for d, t, n in zip(deadline_dates, deadline_types, deadline_notes)]

        deadline_errors = []
        for i, deadline in enumerate(deadlines, 1):
            if not isinstance(deadline, dict):
                continue  # Skip if not a dict
            d_date = deadline.get("deadline_date", "")
            d_type = deadline.get("deadline_type", "")
            if not d_date and not d_type:
                continue  # Skip empty deadline entries
            if not d_date or not d_type:
                deadline_errors.append(f"Deadline {i}: Both date and type are required")
                continue
            try:
                parsed_date = parse(d_date)
                CaseDeadlineRepository.add_deadline(
                    case_id=new_case.case_id,
                    deadline_date=parsed_date,
                    deadline_type=d_type,
                    notes=deadline.get("notes", "").strip() or None
                )
            except Exception as e:
                deadline_errors.append(f"Deadline {i}: Invalid date format - {str(e)}")

        if deadline_errors:
            db.session.rollback()
            return jsonify({
                "error": "Some deadlines had errors",
                "deadline_errors": deadline_errors
            }), 400

        # Process tasks with better error handling
        tasks = []
        if request.is_json:
            tasks = data.get('tasks', [])
        else:
            task_types = request.form.getlist('task_type[]')
            task_descriptions = request.form.getlist('task_description[]')
            task_due_dates = request.form.getlist('task_due_date[]')
            task_statuses = request.form.getlist('task_status[]')
            tasks = [{
                "task_type": t.strip(),
                "description": d.strip(),
                "due_date": du.strip(),
                "status": s.strip()
            } for t, d, du, s in zip(task_types, task_descriptions, task_due_dates, task_statuses)]

        task_errors = []
        for i, task in enumerate(tasks, 1):
            if not isinstance(task, dict):
                continue  # Skip if not a dict
            t_type = task.get("task_type", "").strip()
            if not t_type:
                continue  # Skip empty task entries
            t_due = None
            if task.get("due_date"):
                try:
                    t_due = parse(task["due_date"])
                except Exception as e:
                    task_errors.append(f"Task {i}: Invalid due date format - {str(e)}")
                    continue
            try:
                status_val = task.get("status", "").strip() or "pending"
                CaseTaskRepository.add_task(
                    case_id=new_case.case_id,
                    task_type=t_type,
                    description=task.get("description", "").strip() or None,
                    due_date=t_due,
                    status=status_val
                )
            except Exception as e:
                task_errors.append(f"Task {i}: Failed to add - {str(e)}")

        if task_errors:
            db.session.rollback()
            return jsonify({
                "error": "Some tasks had errors",
                "task_errors": task_errors
            }), 400

        db.session.commit()
        return jsonify({
            "message": "Case added successfully",
            "case_id": new_case.case_id,
            "client_id": client.client_id
        }), 201

    except ValueError as ve:
        db.session.rollback()
        return jsonify({
            "error": str(ve),
            "type": "value_error"
        }), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({
            "error": "An unexpected error occurred",
            "details": str(e)
        }), 500

    

@routes.route('/api/add-client-by-attorney', methods=['GET', 'POST'])
def add_client_by_attorney():
    try:
        context = util_filters.get_user_context()
        
        # Handle GET request for auto-filling law firm
        if request.method == 'GET':
            attorney_id = context.get("id")
            attorney = AttorneyRepository.get_attorney_by_id(attorney_id)
            
            if not attorney or not attorney.law_firm_id:
                return jsonify({"error": "Attorney or law firm not found"}), 404
                
            law_firm = LawFirmRepository.get_law_firm_by_id(attorney.law_firm_id)
            return jsonify({
                "law_firm_name": law_firm.name,
                "law_firm_id": law_firm.id
            })
        
        # Handle POST request
        law_firm_id = context.get("law_firm_id")
        data = request.get_json() if request.is_json else request.form
        
        # Run conflict check
        checker = ConflictChecker(db.session, law_firm_id)
        conflict_results = checker.check_conflicts(new_client=data)
        
        conflicts = checker.format_conflict_results(conflict_results)
        if conflicts:
            log_entry = ConflictCheckLog(
                triggered_by_user_id=context.get("id"),
                triggered_by_user_role=context.get("user_role"),
                law_firm_id=law_firm_id,
                client_name_checked=data.get('client_name'),
                result={'conflicts': conflicts},
                timestamp=datetime.utcnow()
            )
            db.session.add(log_entry)
            db.session.commit()
            
            return jsonify({
                'warning': 'Potential conflicts detected',
                'conflicts': conflicts,
                'check_id': log_entry.id
            }), 200

        # If no conflicts, proceed with client creation
        custom_fields = data.get('custom_fields', {})

        client_name = data.get('client_name')
        email = data.get('email')
        phone_number = data.get('phone_number', None)
        address = data.get('address', None)
        description = data.get('description', None)
        
        # Get law firm from context
        attorney_id = context.get("id")
        attorney = AttorneyRepository.get_attorney_by_id(attorney_id)
        if not attorney or not attorney.law_firm_id:
            return jsonify({"error": "Attorney or law firm not found"}), 404
            
        law_firm = LawFirmRepository.get_law_firm_by_id(attorney.law_firm_id)
        law_firm_id = law_firm.id
        
        primary_attorney_name = data.get('primary_attorney_name')
        collaborating_names_str = data.get('collaborating_attorney_names', '')

        errors = []

        # Email validation and conflict check
        if email:
            # Validate email format
            email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
            if not re.match(email_regex, email):
                errors.append("Invalid email format.")
            
            # Check for email conflict
            existing_client_with_email = ClientRepository.get_client_by_email(email)
            if existing_client_with_email:
                return jsonify({
                    "error": f"Email '{email}' is already associated with another client",
                    "field": "email",
                    "type": "email_conflict"
                }), 409

        # Other validations
        phone_regex = r'^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$'
        if phone_number and not re.match(phone_regex, phone_number):
            errors.append("Invalid phone number format.")

        address_regex = r'^\d+\s+\w+\s+\w+.*,\s*\w+,\s*[A-Za-z]{2},\s*\d{5}(-\d{4})?$'
        if address and not re.match(address_regex, address):
            errors.append("Invalid address format.")

        if errors:
            return jsonify({"error": errors}), 400

        # Convert names to IDs
        primary_attorney_id = None
        if primary_attorney_name:
            primary = AttorneyRepository.get_attorney_by_name(primary_attorney_name)
            if not primary:
                return jsonify({"error": f"Primary attorney '{primary_attorney_name}' not found."}), 404
            primary_attorney_id = primary.attorney_id

        collaborating_attorneys = []
        if collaborating_names_str:
            names = [n.strip() for n in collaborating_names_str.split(',')]
            for name in names:
                attorney = AttorneyRepository.get_attorney_by_name(name)
                if attorney:
                    collaborating_attorneys.append(attorney.attorney_id)

        # Check if client exists by name
        existing_client = ClientRepository.get_client_by_name(client_name)
        if existing_client:
            pending_case = session.pop('pending_case', None)
            if pending_case:
                CaseRepository.create_case(
                    case_id=pending_case['case_id'],
                    client_id=existing_client.client_id,
                    client_name=existing_client.name,
                    case_name=pending_case['case_name'],
                    case_type=pending_case['case_type'],
                    case_status=pending_case['case_status'],
                    description=pending_case['description'],
                    custom_fields=custom_fields
                )
                return jsonify({"message": "Existing client found. Case added successfully."}), 201
            else:
                return jsonify({"message": "Client already exists. No new case added."}), 200

        # Generate client credentials
        client_id = f"C{uuid.uuid4().hex[:8].upper()}"
        username = client_name.split()[0].lower() + str(random.randint(1000, 9999))
        temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
        password_hash = generate_password_hash(temp_password)

        # Create new client
        new_client = ClientRepository.create_client(
            client_id=client_id,
            name=client_name,
            username=username,
            password_hash=password_hash,
            email=email,
            phone_number=phone_number,
            address=address,
            description=description,
            law_firm_id=law_firm_id,
            primary_attorney_id=primary_attorney_id,
            collaborating_attorneys=collaborating_attorneys,
            custom_fields=custom_fields
        )

        if not new_client:
            return jsonify({"error": "Client creation failed"}), 500

        # Attach pending case if exists
        pending_case = session.pop('pending_case', None)
        if pending_case:
            CaseRepository.create_case(
                case_id=pending_case['case_id'],
                client_id=new_client.client_id,
                client_name=new_client.name,
                case_name=pending_case['case_name'],
                case_type=pending_case['case_type'],
                case_status=pending_case['case_status'],
                description=pending_case['description'],
                custom_fields=custom_fields
            )

        return jsonify({
            "message": "Client and case added successfully.",
            "username": username,
            "temporary_password": temp_password
        }), 201

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/update-client', methods=['POST'])
def update_client():
    # Get data from JSON if available, otherwise from form data.
    data = request.get_json() if request.is_json else request.form
    
    # Ensure client_id is provided (assumed to be hidden in the form)
    client_id = data.get('client_id')
    if not client_id:
        return jsonify({"error": "Client ID is required"}), 400

    # Extract primary attorney and collaborating attorneys
    primary_attorney_id = data.get('primary_attorney_id')
    collaborating_attorneys_ids = data.get('collaborating_attorneys', [])  # Expecting a list of attorney IDs

    # Call the repository update method; the repository handles exceptions and rollbacks.
    updated_client = ClientRepository.update_client(
        client_id,
        name=data.get('name'),
        username=data.get('username'),
        password_hash=None,  # Omit password update unless explicitly provided.
        email=data.get('email'),
        description=data.get('description'),
        phone_number=data.get('phone_number'),
        address=data.get('address'),
        custom_fields=data.get('custom_fields'),
        primary_attorney_id=primary_attorney_id,
        collaborating_attorneys_ids=collaborating_attorneys_ids
    )

    # If update_client returns None, it means the update failed.
    if not updated_client:
        return jsonify({"error": "Failed to update client"}), 500

    # On success, return a success message.
    return jsonify({"message": "Client updated successfully"}), 2000


@routes.route('/api/update-case', methods=['PUT', 'POST'])
def update_case():
    try:
        # Handle JSON or form data
        if request.is_json:
            data = request.get_json(silent=True)
        else:
            data = request.form.to_dict()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Extract case ID (required)
        case_id = data.get('case_id')
        if not case_id:
            return jsonify({"error": "Case ID is required"}), 400

        # Update case details (if provided)
        case_updates = {
            "client_name": data.get("client_name"),
            "case_name": data.get("case_name"),
            "case_type": data.get("case_type"),
            "case_status": data.get("case_status"),
            "description": data.get("description"),
            "custom_fields": data.get("custom_fields")  # Add custom_fields to updates
        }

        # Call the repository method to update the case
        updated_case = CaseRepository.update_case(case_id, **case_updates)
        if not updated_case:
            return jsonify({"error": "Case not found"}), 404

        # Update deadlines (if provided)
        deadlines = data.get("deadlines", [])
        if isinstance(deadlines, str):  # Handle form data (e.g., deadlines as JSON string)
            try:
                deadlines = json.loads(deadlines)
            except json.JSONDecodeError:
                return jsonify({"error": "Invalid deadlines format"}), 400

        for deadline in deadlines:
            deadline_id = deadline.get("deadline_id")
            if not deadline_id:
                return jsonify({"error": "Deadline ID is required for updates"}), 400

            # Parse deadline_date using dateutil.parser.parse
            deadline_date = deadline.get("deadline_date")
            if deadline_date:
                try:
                    deadline_date = parse(deadline_date)
                except ValueError:
                    return jsonify({"error": "Invalid deadline_date format"}), 400

            deadline_updates = {
                "deadline_date": deadline_date,
                "deadline_type": deadline.get("deadline_type"),
                "notes": deadline.get("notes")
            }

            # Call the repository method to update the deadline
            updated_deadline = CaseRepository.update_deadline(deadline_id, **deadline_updates)
            if not updated_deadline:
                return jsonify({"error": f"Deadline with ID {deadline_id} not found"}), 404

        # Update tasks (if provided)
        tasks = data.get("tasks", [])
        if isinstance(tasks, str):  # Handle form data (e.g., tasks as JSON string)
            try:
                tasks = json.loads(tasks)
            except json.JSONDecodeError:
                return jsonify({"error": "Invalid tasks format"}), 400

        for task in tasks:
            task_id = task.get("task_id")
            if not task_id:
                return jsonify({"error": "Task ID is required for updates"}), 400

            # Parse due_date using dateutil.parser.parse
            due_date = task.get("due_date")
            if due_date:
                try:
                    due_date = parse(due_date)
                except ValueError:
                    return jsonify({"error": "Invalid due_date format"}), 400

            task_updates = {
                "task_type": task.get("task_type"),
                "description": task.get("description"),
                "due_date": due_date,
                "status": task.get("status")
            }

            # Call the repository method to update the task
            updated_task = CaseRepository.update_task(task_id, **task_updates)
            if not updated_task:
                return jsonify({"error": f"Task with ID {task_id} not found"}), 404

        # Return success message
        return jsonify({"message": "Case, deadlines, and tasks updated successfully"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
    
    
@routes.route('/delete_case/<case_id>', methods=['DELETE'])
def delete_case_route(case_id):
    """Deletes the case using the CaseRepository method."""
    try:
        result = CaseRepository.delete_case(case_id)
        if result is None:
            return jsonify({'message': 'Case not found'}), 404
        else:
            return jsonify({'message': 'Case deleted'}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/client/messaging/send', methods=['POST'])
def post_client_message():
    if 'username' not in session or session.get('user_role') != 'client':
        return jsonify({"error": "Unauthorized"}), 401

    data = request.get_json(silent=True) or request.form
    case_id = data.get('case_id')
    content = data.get('message', "").strip()

    if not case_id or not content:
        return jsonify({"error": "Case ID and message content are required."}), 400

    if len(content) > 750:
        return jsonify({"error": "Message content exceeds 750-character limit."}), 400

    client_cases = CaseRepository.get_cases_by_client(session.get('client_id'))
    if case_id not in [case.case_id for case in client_cases]:
        return jsonify({"error": "Access denied. You can only send messages for your own cases."}), 403

    MessageRepository.create_message(case_id, session['username'], content)
    
    # After creating the message
    case = CaseRepository.get_case_by_id(case_id)
    if case.attorney_id:
        NotificationRepository.create_notification(
            user_id=case.attorney_id,
            user_type='attorney',
            message=f"New message from client in case {case.case_name}",
            notification_type='message',
            related_id=case_id
        )
    return jsonify({"success": True, "message": "Message sent successfully!"})


@routes.route('/api/send-message-attorney', methods=['POST'])
def post_attorney_message():
    # Check if the user is logged in and has the 'attorney' role
    if 'username' not in session or session.get('user_role') != 'attorney':
        # If not authorized, return a 401 error response
        return jsonify({"error": "Unauthorized"}), 401

    # Retrieve JSON data from the request or fallback to form data
    data = request.get_json(silent=True) or request.form

    # Get the case ID from the data
    case_id = data.get('case_id')
    # Get the message content from the data and remove any extra spaces
    content = data.get('content', "").strip()

    # Check if both case ID and message content are provided
    if not case_id or not content:
        # Return a 400 error if either is missing
        return jsonify({"error": "Case ID and message content are required."}), 400

    # Enforce a 750-character limit on the message content
    if len(content) > 750:
        # Return a 400 error if the message is too long
        return jsonify({"error": "Message content exceeds 750-character limit."}), 400

    # Optional: You might add additional checks here to verify that the attorney is allowed
    # to send a message for the selected case. For now, we assume they have access.

    # Create the message using the MessageRepository.
    # Use session['username'] as the sender to ensure the sender is the logged-in attorney.
    MessageRepository.create_message(case_id, session['username'], content)

    # Return a success response in JSON format
    return jsonify({"success": True, "message": "Message sent successfully!"})



@routes.route('/api/case-data', methods=['POST'])
def create_case():
    try:
        data = request.get_json() or request.form
        if not data:
            return jsonify({"error": "Request body must be JSON or form data"}), 400

        is_valid, error_message = validate_case_data(data)
        if not is_valid:
            return jsonify({"error": error_message}), 400

        case = CaseRepository.create_case(data)
        return jsonify({"message": "Case data saved successfully", "case_id": case.case_id}), 201

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/upload-file', methods=['POST'])
def upload_file_with_names():
    try:
        # Parse incoming data
        data = request.get_json(silent=True) or request.form
        client_name = data.get('client_name')
        case_name = data.get('case_name')
        document_type = data.get('document_type')
        custom_type = data.get('custom_type')
        file = request.files.get('file')

        # Get the current user's information
        username = session.get('username')
        user_role = session.get('user_role')
        user_id = session.get('attorney_id') if user_role == 'attorney' else session.get('client_id')
        created_by_value = f"{user_role}:{user_id}"

        if not username or not user_role or not user_id:
            return jsonify({"error": "User not authenticated or missing session data"}), 401

        if not client_name or not case_name or not file:
            return jsonify({"error": "Client name, case, and file are required"}), 400

        # Fetch client and case
        client = ClientRepository.get_client_by_name(client_name)
        if not client:
            return jsonify({"error": f"Client '{client_name}' not found"}), 404

        case = CaseRepository.get_case_by_client_name_and_case_name(client_name, case_name)
        if not case:
            return jsonify({"error": f"Case '{case_name}' not found for client '{client_name}'"}), 404

        if case.client_id != client.client_id:
            return jsonify({"error": f"Case '{case_name}' does not belong to Client '{client_name}'"}), 400

        # Handle custom document type
        if document_type == 'custom':
            if not custom_type:
                return jsonify({"error": "Custom type is required when selecting 'custom'"}), 400
            DocumentRepository.save_custom_document_type(custom_type, created_by=created_by_value)
            document_type = custom_type

        # Validate file
        is_valid, error_message = validate_file(file)
        if not is_valid:
            return jsonify({"error": error_message}), 400

        # Check for existing document
        existing_document = DocumentRepository.get_document_by_original_name_case_and_client(
            original_name=file.filename,
            case_id=case.case_id,
            client_id=client.client_id
        )

        # ⚡ NEW: Handle template processing
        if document_type == 'template':
            if not file.filename.endswith('.docx'):
                return jsonify({"error": "Only .docx templates are supported"}), 400

            # Save temp file
            temp_filename = secure_filename(file.filename)
            temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], temp_filename)
            file.save(temp_path)

            # Process document
            from docx import Document
            doc = Document(temp_path)

            placeholders = {
                '{{client_name}}': client.name,
                '{{client_address}}': client.address,
                '{{client_email}}': client.email,
                '{{case_id}}': case.case_id,
                '{{case_name}}': case.case_name,
                '{{case_status}}': case.case_status
            }

            for paragraph in doc.paragraphs:
                for key, value in placeholders.items():
                    if key in paragraph.text:
                        paragraph.text = paragraph.text.replace(key, str(value) or '')

            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for key, value in placeholders.items():
                            if key in cell.text:
                                cell.text = cell.text.replace(key, str(value) or '')

            # Save filled document
            unique_filename = f"Filled_{uuid.uuid4().hex[:8]}_{secure_filename(file.filename)}"
            filled_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
            doc.save(filled_path)
            print(f"Filled document saved at: {filled_path}")  # Debugging log

            # Delete temp file
            os.remove(temp_path)

            # Adjust original name
            original_name = f"Filled_{file.filename}"

        else:
            # Normal document upload
            unique_filename = f"{uuid.uuid4()}_{secure_filename(file.filename)}"
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)
            print(f"File saved at: {file_path}")  # Debugging log

            original_name = file.filename

        # ⚡ Continue normal document saving
        if existing_document:
            latest_version = DocumentRepository.get_latest_version(existing_document.id)
            new_version_number = (latest_version.version_number + 1) if latest_version else 1

            DocumentRepository.create_document_version(
                document_id=existing_document.id,
                filename=unique_filename,
                version_number=new_version_number,
                uploaded_by=created_by_value
            )
            
            # Log the edit action with version number
            log_document_action(
                document_id=existing_document.id,
                action='edit',
                request=request,
                version_number=new_version_number,
                details={
                    'filename': unique_filename,
                    'original_name': original_name
                }
            )

            return jsonify({
                "message": f"File uploaded successfully as version {new_version_number}",
                "file_path": unique_filename,
                "document_type": document_type,
                "version_number": new_version_number
            }), 200
        else:
            document = DocumentRepository.create_document(
                filename=unique_filename,
                original_name=original_name,
                case_id=case.case_id,
                client_id=client.client_id,
                document_type=document_type,
                uploaded_by=created_by_value
            )

            DocumentRepository.create_document_version(
                document_id=document.id,
                filename=unique_filename,
                version_number=1,
                uploaded_by=created_by_value
            )
            
             # Log the initial upload
            log_document_action(
                document_id=document.id,
                action='upload',
                request=request,
                version_number=1,
                details={
                    'filename': unique_filename,
                    'original_name': original_name
                }
            )

            return jsonify({
                "message": "File uploaded successfully as the first version",
                "file_path": unique_filename,
                "document_type": document_type,
                "version_number": 1
            }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

    
    
@routes.route('/document/<int:document_id>/details-page')
def document_details_page(document_id):
    print(f"DEBUG: Document ID received in /document/<id>/details-page: {document_id}")  # Debugging log

    # Get the current user's role
    user_role = session.get('user_role')  # 'attorney' or 'client'

    # Fetch the document object from the repository
    document = DocumentRepository.get_document_by_id(document_id)

    # Pass the document ID and user role to the template
    return render_template('document_details.html', document=document, document_id=document_id, user_role=user_role)
    


    

@routes.route('/document/<int:document_id>/details', methods=['GET'])
def get_document_details(document_id):
    print(f"DEBUG: Document ID received in /document/<id>/details: {document_id}")  # Debugging log

    
    if not has_role('attorney') and not has_role('client'):
        return jsonify({"error": "Access denied."}), 403

    try:
        # Log the view action
        log_document_action(document_id, 'view', request)
        # Fetch the document details using the repository method
        document = DocumentRepository.get_document_by_id(document_id)
        if not document:
            return jsonify({"error": "Document not found"}), 404

        # Fetch all versions of the document using the repository method
        versions = DocumentRepository.get_versions_by_document_id(document_id)

        # Parse dates using dateutil.parser
        def parse_date(date):
            try:
                if isinstance(date, str):  # Handle string dates
                    return parse(date).isoformat()
                elif isinstance(date, datetime):  # Handle datetime objects
                    return date.isoformat()
                else:
                    return None
            except Exception as e:
                print(f"Error parsing date: {e}")  # Debugging log
                return None
            
        

        document_data = {
            "id": document.id,
            "original_name": document.original_name,
            "document_type": document.document_type,
            "custom_document_type": document.custom_document_type.type_name if document.custom_document_type else None,
            "uploaded_by": document.uploaded_by,
            "uploaded_at": parse_date(document.uploaded_at),
            "versions": [{
                "version_number": version.version_number,
                "filename": version.filename,
                "uploaded_by": version.uploaded_by,
                "uploaded_at": parse_date(version.uploaded_at)
            } for version in versions]
        }

        return jsonify(document_data)

    except Exception as e:
        print(f"Error fetching document details for document_id {document_id}: {e}")
        traceback.print_exc()
        return jsonify({"error": "Internal server error"}), 500

     
@routes.route('/log-activity', methods=['POST'])
@login_required
@roles_required('attorney', 'employee')
def log_activity():
    data = request.json
    case_id = data['case_id']
    activity_type = data['activity_type']
    time_spent = data['time_spent']  # In hours
    description = data.get('description', '')
    context = util_filters.get_user_context()

    user_type = context["user_role"]  # "attorney" or "employee"
    user_id = context['id']  # ID of the attorney or employee

    # Fetch user details to calculate billing rate
    if user_type == "attorney":
        user = AttorneyRepository.get_attorney_by_id(user_id)  # Use AttorneyRepository
    elif user_type == "employee":
        user = EmployeeRepository.get_employee_by_id(user_id)  # Use EmployeeRepository
    else:
        return {"status": "error", "message": "Invalid user type."}, 400

    if not user:
        return {"status": "error", "message": "User not found."}, 404
    print(f"Hourly Rate: {user.hourly_rate}, Type: {type(user.hourly_rate)}")
    print(f"User ID: {user.id}, Hourly Rate: {user.hourly_rate}")
    if not isinstance(user.hourly_rate, (int, float, Decimal)):
        raise ValueError("hourly_rate must be a numeric type")
    if not isinstance(time_spent, (int, float)):
        raise ValueError("time_spent must be a numeric type")

    # Calculate total amount based on hourly rate
    
    hourly_rate = Decimal(str(user.hourly_rate))      # Already Decimal, but wrapped to be safe
    print(f"Hourly Rate (Decimal): {hourly_rate}")

    time_spent_decimal = Decimal(str(time_spent))     # Convert float to Decimal
    print(f"Time Spent (Decimal): {time_spent_decimal}")


    total_amount = (hourly_rate * time_spent_decimal).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    print(f"Calculated Total Amount: {total_amount}")

    # Create activity log
    activity_log = ActivityLogRepository.create_activity_log(
        case_id=case_id,
        activity_type=activity_type,
        time_spent=time_spent,
        description=description,
        total_amount=total_amount,
        attorney_id=user_id if user_type == "attorney" else None,
        employee_id=user_id if user_type == "employee" else None
    )

    mail_response = "No invoice assigned on this activity log"
    if activity_log.invoice_id:
        new_invoice = InvoiceRepository.get_invoice_by_id(activity_log.invoice_id)
        if new_invoice:
            pdf_data = generate_invoice_pdf(new_invoice.invoice_id, context)
            new_invoice.filename = pdf_data["filename"]
            #Send email if theres pdf and "shared" is True
            mail_response = "Invoice marked as no shared"
            if pdf_data.get("pdf_bytes") and new_invoice.shared == True:
                success, error = send_invoice_email(new_invoice, pdf_data["pdf_bytes"], pdf_data["filename"], user)
                if not success:
                    mail_response = error
                else:
                    mail_response = "Email sent correctly" 
            db.session.commit()

    return jsonify({
        "status": "success", 
        "message": "Activity logged successfully.", 
        "activity_id": activity_log.id,
        "mail_response": mail_response
    }), 201

@routes.route('/start-timer', methods=['POST'])
def start_timer():
    """
    Start a timer for an activity.
    Stores the start time in the session.
    """
    case_id = request.json.get('case_id')
    user_type = request.json.get('user_type')  # "attorney" or "employee"
    user_id = request.json.get('user_id')

    if not case_id or not user_type or not user_id:
        return {"status": "error", "message": "Missing required fields."}, 400

    # Validate user type and existence using repositories
    if user_type == "attorney":
        user = AttorneyRepository.get_attorney_by_id(user_id)
    elif user_type == "employee":
        user = EmployeeRepository.get_employee_by_id(user_id)
    else:
        return {"status": "error", "message": "Invalid user type."}, 400

    if not user:
        return {"status": "error", "message": "User not found."}, 404

    # Store the start time in the session
    session['timer'] = {
        'case_id': case_id,
        'user_type': user_type,
        'user_id': user_id,
        'start_time': datetime.now().isoformat()  # Store as ISO format for precision
    }

    return {"status": "success", "message": "Timer started successfully."}

@routes.route('/stop-timer', methods=['POST'])
def stop_timer():
    """
    Stop the timer, calculate time spent, and log the activity.
    """
    if 'timer' not in session:
        return {"status": "error", "message": "No active timer found."}, 400

    # Retrieve timer data from the session
    timer_data = session.pop('timer')  # Remove timer data after stopping
    start_time = datetime.fromisoformat(timer_data['start_time'])
    end_time = datetime.now()
    time_spent = round((end_time - start_time).total_seconds() / 3600, 2)  # Convert seconds to hours

    # Extract other details from the request
    description = request.json.get('description')
    activity_type = request.json.get('activity_type')

    if not description or not activity_type:
        return {"status": "error", "message": "Missing description or activity type."}, 400

    # Fetch user details using repositories
    user_type = timer_data['user_type']
    user_id = timer_data['user_id']

    if user_type == "attorney":
        user = AttorneyRepository.get_attorney_by_id(user_id)
    elif user_type == "employee":
        user = EmployeeRepository.get_employee_by_id(user_id)
    else:
        return {"status": "error", "message": "Invalid user type."}, 400

    if not user:
        return {"status": "error", "message": "User not found."}, 404

    # Calculate total amount based on hourly rate
    total_amount = user.hourly_rate * time_spent

    # Create activity log using the repository
    activity_log = ActivityLogRepository.create_activity_log(
        case_id=timer_data['case_id'],
        activity_type=activity_type,
        time_spent=time_spent,
        description=description,
        total_amount=total_amount,
        attorney_id=user_id if user_type == "attorney" else None,
        employee_id=user_id if user_type == "employee" else None
    )

    return {
        "status": "success",
        "message": "Activity logged successfully.",
        "time_spent": time_spent,
        "total_amount": total_amount,
        "activity_id": activity_log.id
    }
   


@routes.route('/update-law-firm', methods=['POST'])
def update_law_firm():
    data = request.get_json()
    law_firm_id = data.get("id")
    name = data.get("name")
    address = data.get("address")
    contact_email = data.get("contact_email")
    phone_number = data.get("phone_number")

    if not law_firm_id:
        return jsonify({"error": "Law firm ID is required"}), 400

    success = LawFirmRepository.update_law_firm(
        law_firm_id=law_firm_id,
        name=name,
        address=address,
        contact_email=contact_email,
        phone_number=phone_number
    )

    if not success:
        return jsonify({"error": "Failed to update law firm"}), 404

    return jsonify({"message": "Law firm updated successfully"}), 200

# -----------------------------------
# 📅 Calendar Integration Routes
# -----------------------------------
#This generate a Bug when you try to access all routes
#routes = Blueprint('case_routes', __name__)

@routes.route('/calendar')
def calendar_view():
    """Renders the calendar view page."""
    return render_template('calendar.html')

@routes.route('/api/deadlines', methods=['GET'])
@attorney_required  # Restricts access to authenticated attorneys only
def get_deadlines():
    """Fetches all case deadlines and returns them in JSON format."""
    try:
        # Fetch all deadlines from the database
        deadlines = CaseDeadlineRepository.get_all_deadlines()
        events = []
        
        # Map each deadline to the event format expected by FullCalendar
        for deadline in deadlines:
            if not deadline.deadline_date or not deadline.deadline_type:
                continue  # Skip invalid record
            # deadline.deadline_type="court:heearing"
            event = {
                'id': deadline.id,  # Unique identifier for the event
                'case_id': deadline.case_id,  # ID of the case associated with the deadline
                'deadline_type': deadline.deadline_type,  # e.g., "motion", "hearing", etc.
                'title': deadline.deadline_type,  # e.g., "motion", "hearing", etc.
                'start': deadline.deadline_date.isoformat(),  # Convert to ISO string format
                'description': deadline.notes or '',  # Optional notes, default to empty string if not provided
                'extendedProps': {
                    'type': 'court' if 'court' in deadline.deadline_type.lower() else 'deadline',
                    'description': deadline.notes or '',
                },
                'className': 'fc-event-court' if 'court' in deadline.deadline_type.lower() else 'fc-event-deadline'
            }
            events.append(event)
        
        # Return the events as JSON response
        return jsonify(events)
    
    except Exception as e:
        # Log the error (optional) and return a 500 internal server error response
        routes.logger.error(f"Error fetching deadlines: {e}")
        return jsonify({'error': 'Failed to fetch deadlines'}), 500

#####################################
# fetching all cases    
#####################################    
@routes.route('/api/cases', methods=['GET'])
@attorney_required  # Restricts access to authenticated attorneys only
def get_cases():
    """Fetches all cases and supports optional filtering by status and type."""
    try:
        # Fetch all cases from the repository
        cases = CaseRepository.get_all_cases()

        # Get optional query params
        status_filter = request.args.get('status')
        type_filter = request.args.get('case_type')

        # Apply filters only if provided
        if status_filter:
            cases = [c for c in cases if c.case_status and c.case_status.lower() == status_filter.lower()]

        if type_filter:
            cases = [c for c in cases if c.case_type and type_filter.lower() in c.case_type.lower()]

        # Format the response
        case_list = []
        for case in cases:
            if not case.case_id or not case.client_name:
                continue
            case_list.append({
                'case_id': case.case_id,
                'client_name': case.client_name,
                'case_name': case.case_name,
                'case_type': case.case_type,
                'case_status': case.case_status,
                'description': case.description or ''
            })

        return jsonify(case_list), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching cases: {e}")
        return jsonify({'error': 'Failed to fetch cases'}), 500  
    
#################################
# filter cases by case_type  
#################################

@routes.route('/api/case-types', methods=['GET'])
@attorney_required
def get_case_types():
    try:
        cases = CaseRepository.get_all_cases()
        types = sorted({case.case_type for case in cases if case.case_type})
        return jsonify(types), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching case types: {e}")
        return jsonify({'error': 'Failed to load case types'}), 500

#################################
# get all tasks to show  
#################################

@routes.route('/api/tasks', methods=['GET'])
@attorney_required  # Restricts access to authenticated attorneys only
def get_tasks():
    try:
        tasks = CaseTaskRepository.get_all_tasks()
        events = []
        for task in tasks:
            if not task.due_date or not task.task_type:
                continue

                # Simplified assignee information
            assignee = None
            assignee_type = None
            
            if task.assigned_to_employee_id:
                employee = Employee.query.get(task.assigned_to_employee_id)
                if employee:
                    assignee = employee.name
                    assignee_type = "Employee"
            
            elif task.assigned_to_client_id:
                client = Client.query.filter_by(client_id=task.assigned_to_client_id).first()
                if client:
                    assignee = client.name
                    assignee_type = "Client"

            event = {
                'id': task.id,
                'case_id': task.case_id,
                'task_type': task.task_type,
                'start': task.due_date.isoformat(),
                'description': task.description or '',
                'status': task.status,
                'priority': task.priority,
                'assigned_to': assignee,
                'assigned_to_type': assignee_type,
                'assigned_to_id': task.assigned_to_employee_id or task.assigned_to_client_id
            }
            events.append(event)
        
        return jsonify(events)    
    except Exception as e:
        routes.logger.error(f"Error fetching tasks: {e}")
        return jsonify({'error': 'Failed to fetch tasks'}), 500    


 #routes for post resquests api/deadlines and api/tasks    

@routes.route('/api/deadlines', methods=['POST'])
@attorney_required
def create_deadline():
    try:
        data = request.get_json(force=True)

        # Validar campos requeridos
        required_fields = ['case_id', 'deadline_date', 'deadline_type']
        missing = [f for f in required_fields if f not in data]
        if missing:
            return jsonify({"error": f"Missing required fields: {missing}"}), 400

        # Validar fecha
        try:
            deadline_date = parse(data['deadline_date'])
        except (ValueError, ParserError) as e:
            return jsonify({"error": f"Invalid date format: {data['deadline_date']}"}), 400

        # Preprocesar tipo de deadline
        if data.get('is_court_date', False):
            data['deadline_type'] = f"Court: {data['deadline_type']}"

        # Crear deadline
        deadline = CaseDeadlineRepository.add_deadline(
            case_id=data['case_id'],
            deadline_date=deadline_date,
            deadline_type=data['deadline_type'],
            notes=data.get('notes')
        )
        case = CaseRepository.get_case_by_id(data['case_id'])
        if case.client_id:
            NotificationRepository.create_notification(
                user_id=case.client_id,
                user_type='client',
                message=f"New deadline for case {case.case_name}",
                notification_type='deadline',
                related_id=data['case_id']
            )

        return jsonify({
            "id": deadline.id,
            "message": "Deadline created successfully"
        }), 201

    except Exception as e:
        import traceback
        traceback.print_exc()  # Esto imprime el error en consola durante las pruebas
        return jsonify({"error": str(e)}), 500


##############################
# creating the task 
# Task manangement feature
##############################

@routes.route('/task_management')
@attorney_required  # Restricts access to authenticated attorneys only
def task_create():
    """Renders the task_management view page."""
    return render_template('task_management.html')



@routes.route('/api/tasks', methods=['POST'])
@attorney_required  # Restricts access to authenticated attorneys only
def create_task():
    try:
        data = request.get_json()
        
        print(f"DEBUG: Data received in /api/tasks: {data}")  # Debugging log
                
            # Validate required fields
        if not all(field in data for field in ['case_id', 'task_type']):
            return jsonify({"error": "Missing required fields"}), 400

            # Validate priority if provided
        if 'priority' in data and data['priority'] not in ['low', 'medium', 'high']:
            return jsonify({"error": "Invalid priority value"}), 400

        # Parse due date if provided
        due_date = parse(data['due_date']) if data.get('due_date') else None

        # Create task
        task = CaseTaskRepository.add_task(
            case_id=data['case_id'],
            task_type=data['task_type'],
            description=data.get('description'),
            due_date=data.get('due_date'),
            status=data.get('status', 'pending'),
            priority=data.get('priority', 'medium'),
            assigned_to_employee_id=data.get('assigned_to_employee_id'),
            assigned_to_client_id=data.get('assigned_to_client_id')
        )
        return jsonify({
            "id": task.id,
            "message": "Task created successfully"
        }), 201
        
    except ValueError as e:
        return jsonify({"error": "Invalid date format"}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/assignable-users', methods=['GET'])
def get_assignable_users():
    try:
        employees = Employee.query.all()
        clients = Client.query.all()

        assignable = []

        for emp in employees:
            assignable.append({
                "id": emp.id,
                "name": emp.name,
                "type": "Employee"
            })

        for client in clients:
            assignable.append({
                "id": client.client_id,
                "name": client.name,
                "type": "Client"
            })

        return jsonify(assignable), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500

###########################################
# put and delete routes for tasks_management
###########################################

@routes.route('/api/tasks/<int:task_id>', methods=['PUT'])
@attorney_required  # Restricts access to authenticated attorneys only
def update_task(task_id):
    try:
        data = request.get_json()
        
        # Validate required fields
        if not all(field in data for field in ['case_id', 'task_type']):
            return jsonify({"error": "Missing required fields"}), 400

        # Validate priority if provided
        if 'priority' in data and data['priority'] not in ['low', 'medium', 'high', 'critical']:
            return jsonify({"error": "Invalid priority value"}), 400

        # Validate status if provided
        valid_statuses = ['pending', 'in_progress', 'completed', 'cancelled']
        if 'status' in data and data['status'] not in valid_statuses:
            return jsonify({"error": "Invalid status value"}), 400

        # Update task
        updated_task = CaseTaskRepository.update_task(
            task_id=task_id,
            task_type=data['task_type'],
            description=data.get('description'),
            due_date=data.get('due_date'),
            status=data.get('status'),
            priority=data.get('priority'),
            assigned_to_employee_id=data.get('assigned_to_employee_id'),
            assigned_to_client_id=data.get('assigned_to_client_id')
        )

        if not updated_task:
            return jsonify({"error": "Task not found"}), 404

        return jsonify({
            "id": updated_task.id,
            "message": "Task updated successfully"
        }), 200
        
    except ValueError as e:
        return jsonify({"error": "Invalid date format"}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/tasks/<int:task_id>', methods=['DELETE'])
@attorney_required  # Restricts access to authenticated attorneys only
def delete_task(task_id):
    try:
        success = CaseTaskRepository.delete_task(task_id)
        
        if not success:
            return jsonify({"error": "Task not found"}), 404

        return jsonify({
            "message": "Task deleted successfully"
        }), 200
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
# 
####
#Remembering
#Add .env variable and set loggin into dev
####
@routes.route('/api/invoices', methods=['GET'])
@login_required
def get_invoices():
    try:
        # pagination
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))
        # Filters
        filters = util_filters.build_invoice_filters()

        query = InvoiceRepository.get_all_query(**filters)

        paginator = Paginator(query, page, per_page)
        result = paginator.paginate()

        # New: Serialize invoices + add expenses and activity logs associated
        serialized_invoices = InvoiceRepository.append_expenses_to_invoices(result["items"])

        return jsonify({
            "data": serialized_invoices,
            "pagination": result["meta"]
        })

    except Exception as e:
        logging.error("Error fetching invoices", exc_info=True)
        return jsonify({
            "error": "An error appear getting the invoces.",
            "details": str(e)
        }), 500

#Post route for creating invoices
@routes.route('/api/invoices', methods=['POST'])
@roles_required('attorney', 'employee')
@write_permission_required
def create_invoice():
    data = request.get_json()
    context = util_filters.get_user_context()

    try:
        # Validate requested payload
        valid_data = validators.validate_invoice_payload(data, is_update=False)
        # add metadata to context
        valid_data["created_by_id"] = context["id"]
        valid_data["created_by_type"] = context["user_role"]
        valid_data["shared"] = data.get("shared", False)
        # Create invoice
        invoice = InvoiceRepository.create_invoice_ifowner(valid_data)
        #Generate PDF
        pdf = {}
        try:
            pdf = generate_invoice_pdf(invoice.invoice_id, context)
            invoice.filename = pdf["filename"]
        except Exception as pdf_error:
            # Continiue anyways with invoice creation
            invoice.filename = None
        # Add context-based metadata
        id_user = context["id"]
        role = context["user_role"]
        if role == "attorney":
            attorney = AttorneyRepository.get_attorney_by_id(id_user)
            if attorney:
                representative = attorney
        elif role == "employee":
            employee = EmployeeRepository.get_employee_by_id(id_user)
            if employee:
                representative = employee
        #Sen email if theres pdf and "shared" is True
        mail_response = "Invoice marked as no shared"
        if pdf.get("pdf_bytes") and invoice.shared == True:
            success, error = send_invoice_email(invoice, pdf["pdf_bytes"], pdf["filename"], representative)
            if not success:
                mail_response = error
            else:
                mail_response = "Email sent correctly" 
        # Save invoice also if theres an error on pdf
        db.session.commit()

        return jsonify({
            "message": "Invoice created successfully",
            "invoice_id": invoice.invoice_id,
            "filename": invoice.filename or "No PDF generated",
            "mail": mail_response
        }), 201

    except ValueError as ve:
        db.session.rollback()
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

# PUT route for updating invoices
@routes.route('/api/invoices/<int:invoice_id>', methods=['PUT'])
@roles_required('attorney', 'employee')
@write_permission_required
def update_invoice(invoice_id):
    data = request.get_json()
    context = util_filters.get_user_context()

    if not data:
        return jsonify({"error": "Invalid JSON data"}), 400

    try:
        # Verify if the request user is assigned to the case
        InvoiceRepository.get_invoice_if_owner(
            invoice_id, context["id"], context["user_role"]
        )

        # Validate the payload
        valid_data = validators.validate_invoice_payload(data, is_update=True)

        # Add user metadata for the update
        valid_data["updated_by_id"] = context["id"]
        valid_data["updated_by_type"] = context["user_role"]

        # Update the invoice
        updated_invoice = InvoiceRepository.update_invoice(invoice_id, valid_data)
        # Try to send pdf my email
        if context["user_role"] == "attorney":
            attorney = AttorneyRepository.get_attorney_by_id(context["id"])
            if attorney:
                representative = attorney
        elif context["id"] == "employee":
            employee = EmployeeRepository.get_employee_by_id(context["id"])
            if employee:
                representative = employee
        mail_response = "No email sent"
        if updated_invoice.filename and updated_invoice.shared == True:
            case_id = updated_invoice.case_id
            pdf_path = os.path.join(os.getcwd(), "generated_pdfs", case_id, f"invoice_{invoice_id}", updated_invoice.filename)
            if os.path.exists(pdf_path):
                with open(pdf_path, "rb") as f:
                    pdf_bytes = f.read()
                success, error = send_invoice_email(updated_invoice, pdf_bytes, updated_invoice.filename, representative)
                if not success:
                    mail_response = error
                else:
                    mail_response = "Email sent correctly"
            else:
                mail_response = "PDF file not found"
        elif updated_invoice.shared and not updated_invoice.filename:
            mail_response = "Invoice is shared but has no PDF file"

        return jsonify({
            "message": "Invoice updated successfully",
            "invoice_id": updated_invoice.invoice_id,
            "mail_Response": mail_response
        }), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400

    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500
    
@routes.route('/api/invoices/<int:invoice_id>', methods=['DELETE'])
@roles_required('attorney', 'employee')
@write_permission_required
def delete_invoice(invoice_id):

    context = util_filters.get_user_context()

    try:
        # 1. Verify if user is assigned to the case and invoice
        invoice = InvoiceRepository.get_invoice_if_owner(invoice_id, context["id"], context["user_role"])

        # 2. Delete the invoice
        InvoiceRepository.delete_invoice(invoice)

        return jsonify({"message": "Invoice deleted successfully"}), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404

    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML
from flask import make_response, render_template

@routes.route("/api/invoices/pdf/test/<int:invoice_id>", methods=["GET"])
@login_required
def generate_test_pdf(invoice_id):
    try:
        context = util_filters.get_user_context()
        user_role = context.get("user_role")
        if user_role != "admin":
            raise PermissionError("This route is only for admin role")
        result = generate_invoice_pdf(invoice_id, context)

        response = make_response(result["pdf_bytes"])
        response.headers["Content-Type"] = "application/pdf"
        response.headers["Content-Disposition"] = f"inline; filename=test_invoice.pdf"
        return response

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

@routes.route("/download/<int:invoice_id>/<filename>", methods=["GET"])
def download_file(invoice_id, filename):
    try:
        context = util_filters.get_user_context()
        invoice = InvoiceRepository.get_invoice_by_id(invoice_id)
        case = CaseRepository.get_case_by_id(invoice.case_id)
        if not case:
            raise ValueError("No case found")
        
        user_role = context.get("user_role")
        user_id = context.get("id")
        if user_role == "attorney":
            if case.attorney_id != user_id:
                raise PermissionError("No authorized to see invoices for this case")
        elif user_role == "employee":
            if case.employee_id != user_id:
                raise PermissionError("No authorized to see invoices for this case")
        elif user_role == "client":
            if case.client_id != user_id:
                raise PermissionError("No authorized to see invoices for this case")
            if invoice.shared == False:
                return jsonify({"error": "Invoice file not shared with client"}), 404
        elif user_role == "admin":
            pass
        else:
            raise PermissionError("No role allowed")

        pdf_dir = os.path.join(
            os.getcwd(), "generated_pdfs", case.case_id, f"invoice_{invoice_id}"
        )
        return send_from_directory(pdf_dir, filename, as_attachment=True)
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403
    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400 
    except Exception as e:
        return jsonify({"error": str(e)}), 500


##----------------------------
#Expenses routes
##----------------------------
@routes.route('/api/expenses', methods=['GET'])
@roles_required('attorney', 'employee')
@write_permission_required
def get_expenses():
    try:
        # pagination
        page = validators.safe_int(request.args.get("page"), default=1)
        per_page = validators.safe_int(request.args.get("per_page"), default=10)

        incurred_at_str = request.args.get("incurred_at")

        case_id = request.args.get("case_id")
        if case_id:
            validators.validate_case_exists(case_id)
        invoice_id = request.args.get("invoice_id")
        if invoice_id:
            validators.validate_invoice_exists(invoice_id)

        if incurred_at_str:
            # Validate the date format
            incurred_at = validators.validate_date_field(incurred_at_str, "incurred_at", date_format="%Y-%m-%d")
            if incurred_at > datetime.now(tz=timezone.utc):
                raise ValueError("incurred_at cannot be in the future")

        # Filters
        filters = util_filters.build_expense_filters()

        query = ExpensesRepository.get_expenses_by_case(**filters)

        paginator = Paginator(query, page, per_page)
        result = paginator.paginate()

        serialized_invoices = [serialize_expense(inv) for inv in result["items"]]

        return jsonify({
            "data": serialized_invoices,
            "pagination": result["meta"]
        })
    except ValueError as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error("Error fetching invoices", exc_info=True)
        return jsonify({
            "error": "An error appear getting the invoces.",
            "details": str(e)
        }), 500
    
@routes.route('/api/expenses', methods=['POST'])
@roles_required('attorney', 'employee')
@write_permission_required
def create_expense():
    data = request.get_json()
    context = util_filters.get_user_context()
    
    try:
        valid_data = validators.validate_expense_payload(data, is_update=False)

        # Add context-based metadata
        id = context["id"]
        role = context["user_role"]

        if role == "attorney":
            attorney = AttorneyRepository.get_attorney_by_id(id)
            if attorney:
                representative = attorney
        elif role == "employee":
            employee = EmployeeRepository.get_employee_by_id(id)
            if employee:
                representative = employee

        expense = ExpensesRepository.create_expense_ifowner(valid_data, id, role)

        mail_response = "No invoice assigned on this expense"
        if expense.invoice_id:
            new_invoice = InvoiceRepository.get_invoice_by_id(expense.invoice_id)
            if new_invoice:
                pdf_data = generate_invoice_pdf(new_invoice.invoice_id, context)
                new_invoice.filename = pdf_data["filename"]
                #Send email if theres pdf and "shared" is True
                mail_response = "Invoice marked as no shared"
                if pdf_data.get("pdf_bytes") and new_invoice.shared == True:
                    success, error = send_invoice_email(new_invoice, pdf_data["pdf_bytes"], pdf_data["filename"], representative)
                    if not success:
                        mail_response = error
                    else:
                        mail_response = "Email sent correctly" 
                db.session.commit()

        return jsonify({
            "message": "Expense created successfully",
            "mail_response": mail_response,
            "expense_id": expense.expense_id
        }), 201

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403

print("🔥 routes.py loaded and executed")

@routes.route('/api/expenses/<int:expense_id>', methods=['PUT'])
@roles_required('attorney', 'employee')
@write_permission_required
def update_expense(expense_id):
    data = request.get_json()
    context = util_filters.get_user_context()
    
    try:
        valid_data = validators.validate_expense_payload(data, is_update=True)

        # Add context-based metadata
        id = context["id"]
        role = context["user_role"]
        #update the expense
        expense = ExpensesRepository.update_expense_ifowner(valid_data, id, role, expense_id)
        # Generate PDF if theres one
        pdf_message = "No PDF on this invoice"
        if role == "attorney":
            attorney = AttorneyRepository.get_attorney_by_id(id)
            if attorney:
                representative = attorney
        elif role == "employee":
            employee = EmployeeRepository.get_employee_by_id(id)
            if employee:
                representative = employee
        mail_response = "No invoice assigned on this expense"
        if expense.invoice_id:
            new_invoice = InvoiceRepository.get_invoice_by_id(expense.invoice_id)
            if new_invoice:
                pdf_data = generate_invoice_pdf(new_invoice.invoice_id, context)
                new_invoice.filename = pdf_data["filename"]
                pdf_message = pdf_data["filename"]
                #Send email if theres pdf and "shared" is True
                mail_response = "Invoice marked as no shared"
                if pdf_data.get("pdf_bytes") and new_invoice.shared == True:
                    success, error = send_invoice_email(new_invoice, pdf_data["pdf_bytes"], pdf_data["filename"], representative)
                    if not success:
                        mail_response = error
                    else:
                        mail_response = "Email sent correctly" 
                db.session.commit()

        return jsonify({
            "message": "Expense updated successfully",
            "expense_id": expense.expense_id,
            "pdf_message": pdf_message,
            "mail_response": mail_response
        }), 201

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403

@routes.route('/api/expenses/<int:expense_id>', methods=['DELETE'])
@roles_required('attorney', 'employee')
@write_permission_required
def delete_expense(expense_id):

    context = util_filters.get_user_context()

    try:
        # Add context-based metadata
        id = context["id"]
        role = context["user_role"]
        # 1. Verify if user is assigned to expense by case
        expense = ExpensesRepository.get_expense_if_owner(expense_id, context["id"], context["user_role"])
        # 2. Delete expense
        ExpensesRepository.delete_expense_and_update(expense)
        # Generate again PDF is theres an invoce_id
        pdf_message = "No PDF on this invoice"
        if role == "attorney":
            attorney = AttorneyRepository.get_attorney_by_id(id)
            if attorney:
                representative = attorney
        elif role == "employee":
            employee = EmployeeRepository.get_employee_by_id(id)
            if employee:
                representative = employee
        mail_response = "No invoice assigned on this expense"
        if expense.invoice_id:
            new_invoice = InvoiceRepository.get_invoice_by_id(expense.invoice_id)
            if new_invoice:
                pdf_data = generate_invoice_pdf(new_invoice.invoice_id, context)
                new_invoice.filename = pdf_data["filename"]
                pdf_message = pdf_data["filename"]
                #Send email if theres pdf and "shared" is True
                mail_response = "Invoice marked as no shared"
                if pdf_data.get("pdf_bytes") and new_invoice.shared == True:
                    success, error = send_invoice_email(new_invoice, pdf_data["pdf_bytes"], pdf_data["filename"], representative)
                    if not success:
                        mail_response = error
                    else:
                        mail_response = "Email sent correctly" 
                db.session.commit()

        return jsonify({
            "message": "Expense deleted successfully",
            "mail_response": mail_response,
            "pdf_message": pdf_message
        }), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 404

    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

print("🔥routes.py loaded and execute")


@routes.route('/api/contacts', methods=['GET'])
def get_non_client_contacts():
    """
    Protected route to retrieve all non-client contacts:
    - Staff (Attorneys, Employees)
    - (Future) External contacts
    Returns a JSON list of objects with fields:
    [id, name, email, phone, case_link, last_contact, contact_type]
    """
    user_role = session.get('user_role')
    if user_role not in ['attorney', 'admin']:
        return jsonify({"error": "Unauthorized"}), 401

    try:
        attorney_data = get_attorney_contacts()
        employee_data = get_employee_contacts()
    except Exception as e:
        return jsonify({"error": f"Failed to retrieve contacts: {str(e)}"}), 500

    all_contacts = attorney_data + employee_data  # + external_data (future)
    return jsonify(all_contacts), 200

def get_attorney_contacts():
    attorneys = AttorneyRepository.get_all_attorneys()
    data = []

    for atty in attorneys:
        last_login_str = atty.last_login.isoformat() if atty.last_login else "N/A"
        date_added_str = atty.date_added.isoformat() if hasattr(atty, 'date_added') and atty.date_added else "N/A"

        data.append({
            "id": atty.id,  # Added id field
            "name": atty.name,
            "email": atty.email,
            "phone": atty.phone_number,  # Added phone field
            "case_link": "N/A",
            "last_contact": last_login_str,  # Use last_login as last_contact for consistency
            "date_added": date_added_str,
            "contact_type": "Attorney"
        })

    return data


def get_employee_contacts():
    employees = EmployeeRepository.get_all_employees()
    data = []

    for emp in employees:
        last_login_str = emp.last_login.isoformat() if emp.last_login else "N/A"
        date_added_str = emp.date_added.isoformat() if hasattr(emp, 'date_added') and emp.date_added else "N/A"

        data.append({
            "id": emp.id,  # Added id field
            "name": emp.name,
            "email": emp.email,  # Added email field
            "phone": emp.phone_number,  # Added phone field
            "case_link": "N/A",
            "last_contact": last_login_str,  # Use last_login as last_contact for consistency
            "date_added": date_added_str,
            "contact_type": "Employee"
        })

    return data

# --------------------- CREATE ---------------------
@routes.route('/api/external-contacts', methods=['POST'])
def create_external_contact():
    """Create a new external contact (attorney access only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    data = request.get_json() or request.form
    name = data.get('name')
    contact_type = data.get('contact_type')
    email = data.get('email')
    phone = data.get('phone')
    role = data.get('role')
    case_link = data.get('case_link')
    last_contact = data.get('last_contact')

    if not name:
        return jsonify({"error": "Name is required"}), 400

    # Email validation
    if email:
        import re
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return jsonify({"error": "Invalid email format"}), 400

    # Phone validation
    if phone:
        import re
        if not re.match(r'^[\d\s\(\)\-+]{10,20}$', phone):
            return jsonify({"error": "Invalid phone number format"}), 400

    # Parse last_contact if provided
    if last_contact:
        try:
            from datetime import datetime
            last_contact = datetime.fromisoformat(last_contact.replace('Z', '+00:00'))
        except ValueError:
            last_contact = None

    try:
        new_contact = ExternalContactRepository.create_contact(
            name=name,
            contact_type=contact_type,
            email=email,
            phone=phone,
            role=role,
            case_link=case_link,
            last_contact=last_contact
        )
        if not new_contact:
            return jsonify({"error": "Failed to create contact"}), 500

        return jsonify({
            "message": "Contact created successfully",
            "contact_id": new_contact.id,
            "contact": {
                "id": new_contact.id,
                "name": new_contact.name,
                "contact_type": new_contact.contact_type,
                "email": new_contact.email,
                "phone": new_contact.phone,
                "role": new_contact.role,
                "case_link": new_contact.case_link
            }
        }), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --------------------- READ ---------------------
@routes.route('/api/external-contacts', methods=['GET'])
def get_all_external_contacts():
    """Retrieve all external contacts (attorney access only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    try:
        contacts = ExternalContactRepository.get_all_contacts()
        contact_list = [{
            "id": contact.id,
            "name": contact.name,
            "contact_type": contact.contact_type or 'External',
            "email": contact.email,
            "phone": contact.phone,
            "role": contact.role,
            "case_link": contact.case_link,
            "last_contact": contact.last_contact.isoformat() if contact.last_contact else None
        } for contact in contacts]

        return jsonify({"contacts": contact_list}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@routes.route('/api/external-contacts/<int:contact_id>', methods=['GET'])
def get_external_contact_by_id(contact_id):
    """Retrieve a single external contact by ID (attorney access only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    try:
        contact = ExternalContactRepository.get_contact_by_id(contact_id)
        if not contact:
            return jsonify({"error": "Contact not found"}), 404

        return jsonify({
            "id": contact.id,
            "name": contact.name,
            "contact_type": contact.contact_type or 'External',
            "email": contact.email,
            "phone": contact.phone,
            "role": contact.role,
            "case_link": contact.case_link,
            "last_contact": contact.last_contact.isoformat() if contact.last_contact else None
        }), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --------------------- UPDATE ---------------------
@routes.route('/api/external-contacts/<int:contact_id>', methods=['PUT'])
def update_external_contact(contact_id):
    """Update an external contact's information (attorney access only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    data = request.get_json() or request.form
    name = data.get('name')
    contact_type = data.get('contact_type')
    email = data.get('email')
    phone = data.get('phone')
    role = data.get('role')
    case_link = data.get('case_link')
    last_contact = data.get('last_contact')

    # Parse last_contact if provided
    if last_contact:
        try:
            from datetime import datetime
            last_contact = datetime.fromisoformat(last_contact.replace('Z', '+00:00'))
        except ValueError:
            last_contact = None

    try:
        updated_contact = ExternalContactRepository.update_contact(
            contact_id,
            name=name,
            contact_type=contact_type,
            email=email,
            phone=phone,
            role=role,
            case_link=case_link,
            last_contact=last_contact
        )
        if not updated_contact:
            return jsonify({"error": "Contact not found or update failed"}), 404

        return jsonify({
            "message": "Contact updated successfully",
            "contact": {
                "id": updated_contact.id,
                "name": updated_contact.name,
                "contact_type": updated_contact.contact_type,
                "email": updated_contact.email,
                "phone": updated_contact.phone,
                "role": updated_contact.role,
                "case_link": updated_contact.case_link,
                "last_contact": updated_contact.last_contact.isoformat() if updated_contact.last_contact else None
            }
        }), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --------------------- DELETE ---------------------
@routes.route('/api/external-contacts/<int:contact_id>', methods=['DELETE'])
def delete_external_contact(contact_id):
    """Delete an external contact (attorney access only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    try:
        success = ExternalContactRepository.delete_contact(contact_id)
        if not success:
            return jsonify({"error": "Contact not found or could not be deleted"}), 404

        return jsonify({"message": "Contact deleted successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
    
    # --------------------- ASSIGN CONTACT TO CASE ---------------------
@routes.route('/api/case-contact-assignments', methods=['POST'])
def assign_external_contact_to_case():
    """Assign an external contact to a case with a role (attorney-only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    data = request.get_json() or request.form
    case_id = data.get('case_id')
    contact_id = data.get('external_contact_id')
    role = data.get('role')

    if not (case_id and contact_id and role):
        return jsonify({"error": "case_id, external_contact_id, and role are required"}), 400

    # Optional: Validate case and contact exist
    if not CaseRepository.get_case_by_id(case_id):
        return jsonify({"error": "Case not found"}), 404

    if not ExternalContactRepository.get_contact_by_id(contact_id):
        return jsonify({"error": "External contact not found"}), 404

    assignment = CaseContactAssignmentRepository.assign_contact_to_case(case_id, contact_id, role)
    if not assignment:
        return jsonify({"error": "Failed to assign contact to case"}), 500

    return jsonify({
        "message": "Contact assigned successfully",
        "assignment_id": assignment.id
    }), 201


@routes.route('/api/case-contact-assignments/<string:case_id>', methods=['GET'])
def get_case_contact_assignments(case_id):
    """Get all assigned external contacts for a case (attorney-only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    assignments = CaseContactAssignmentRepository.get_assignments_by_case(case_id)
    result = []
    for a in assignments:
        result.append({
            "assignment_id": a.id,
            "contact_id": a.external_contact_id,
            "contact_name": a.external_contact.name,
            "role": a.role,
            "assigned_date": a.assigned_date.isoformat()
        })

    return jsonify(result), 200


@routes.route('/api/case-contact-assignments/<int:assignment_id>', methods=['PUT'])
def update_case_contact_role(assignment_id):
    """Update the role of an existing assignment (attorney-only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    data = request.get_json() or request.form
    role = data.get('role')

    if not role:
        return jsonify({"error": "New role is required"}), 400

    updated = CaseContactAssignmentRepository.update_assignment(assignment_id, role)
    if not updated:
        return jsonify({"error": "Assignment not found or update failed"}), 404

    return jsonify({"message": "Role updated successfully"}), 200


@routes.route('/api/case-contact-assignments/<int:assignment_id>', methods=['DELETE'])
def delete_case_contact_assignment(assignment_id):
    """Remove a contact assignment from a case (attorney-only)."""
    if not has_role('attorney'):
        return jsonify({"error": "Access denied. Attorneys only."}), 403

    success = CaseContactAssignmentRepository.delete_assignment(assignment_id)
    if not success:
        return jsonify({"error": "Assignment not found or could not be deleted"}), 404

    return jsonify({"message": "Assignment deleted successfully"}), 200


@routes.route('/api/get-all-cases-by-attorney', methods=['GET'])
def get_all_cases_by_attorney():
    try:
        if 'user_role' not in session or session['user_role'] != 'attorney':
            return jsonify([]), 200  # Return empty array instead of error
        
        # Fix: Use user_id from session, fallback to attorney_id for backward compatibility
        attorney_id = session.get('user_id') or session.get('attorney_id')
        
        if not attorney_id:
            return jsonify([]), 200  # Return empty array instead of error
        
        cases = CaseRepository.get_all_cases_by_attorney(attorney_id)
        
        # Always return an array, even if empty
        result = [
            {
                "case_id": case.case_id,
                "case_name": case.case_name,
                "case_type": case.case_type,
                "case_status": case.case_status,
                "client_name": case.client_name,
            } for case in cases
        ]
        return jsonify(result)
    except Exception as e:
        # Return empty array on error to maintain frontend expectations
        # Log the error for debugging
        current_app.logger.error(f"Error fetching cases: {str(e)}")
        return jsonify([]), 200
    
@routes.route('/mock-case-detail', methods=['GET'])
def mock_case_detail():
    case_data = {
    "case_id": 101,
    "case_name": "Johnson vs. Apex Corp",
    "case_type": "Civil Litigation",
    "case_status": "Open",
    "description": "This case involves a contract dispute between Johnson and Apex Corp.",
    "custom_fields": {
        "priority": "High",
        "jurisdiction": "California"
    },
    "user_role": "Attorney",  # e.g., fetched from session

    "deadlines": [],

    "tasks": [],

    "documents": [
         {
        "id": 1,
        "filename": "Filled_abc123_welcome_template.docx",
        "original_name": "Filled_welcome_template.docx",
        "uploaded_at": "2025-05-01T12:00:00Z"
      },
         {
             "id": 2,
             "filename": "0b17f48b-2899-4db5-9437-d869547d54a8_test1.pdf",
                "original_name": "test1",
                "uploaded_at": "2025-05-02T12:00:00Z"
             }],

    "messages": [
         {
            "sender": "john_attorney",
            "content": "Initial attorney message",
            "timestamp": "2025-04-22T10:00:00Z"},
        { 
         "sender": "john_client",
         "content": "Client response to attorney",
            "timestamp": "2025-04-22T11:00:00Z"}
        ]
    }
    return render_template('case_detail.html', case=case_data)

# ==============================================
# CASE OVERVIEW PANEL
# ==============================================
    
@routes.route('/case-overview')
def case_overview():
    """Render the Case Overview Panel.
    
    Returns:
        Rendered template for case_overview.html
    """
    return render_template('case_overview.html') 


# ==============================================
# CASE ACTIVITIES API ENDPOINT
# ==============================================
@routes.route('/api/case-activities/<case_id>', methods=['GET'])
def get_case_activities(case_id):
    """Get all activities for a specific case.
    
    Args:
        case_id: The ID of the case to fetch activities for
        
    Returns:
        JSON response containing:
        - List of activity objects (id, activity_type, description, timestamp)
        - 200 status on success
        - Error message and 500 status on failure
    """
    try:
        # Fetch activities from repository
        activities = ActivityLogRepository.get_activities_by_case(case_id)
        
        # Format response data
        response_data = [{
            "id": a.id,
            "activity_type": a.activity_type,
            "description": a.description,
            "timestamp": a.timestamp.isoformat() if a.timestamp else None
        } for a in activities]
        
        return jsonify(response_data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# ==============================================
# CASE DOCUMENTS API ENDPOINT
# ==============================================
@routes.route('/api/case-documents/<case_id>', methods=['GET'])
def get_case_documents(case_id):
    """Get all documents associated with a specific case.
    
    Args:
        case_id: The ID of the case to fetch documents for
        
    Returns:
        JSON response containing:
        - List of document objects (id, original_name, document_type, uploaded_at)
        - 200 status on success
        - Error message and 500 status on failure
    """
    try:
        # Fetch documents from repository
        documents = DocumentRepository.get_documents_by_case_id(case_id)
        
        # Format response data
        response_data = [{
            "id": d.id,
            "original_name": d.original_name,
            "document_type": d.document_type,
            "uploaded_at": d.uploaded_at.isoformat() if d.uploaded_at else None
        } for d in documents]
        
        return jsonify(response_data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500



@routes.route('/documents', methods=['GET'])
def view_all_documents():
    if 'username' not in session or session.get('user_role') != 'attorney':
        return redirect(url_for('auth.login'))

    documents = DocumentRepository.get_all_documents()
    return render_template('all_documents.html', documents=documents)

# ==============================================
# CASE TASKS API ENDPOINT
# ==============================================
@routes.route('/api/case-tasks/<case_id>', methods=['GET'])
def get_case_tasks(case_id):
    """Get all tasks associated with a specific case.
    
    Args:
        case_id: The ID of the case to fetch tasks for
        
    Returns:
        JSON response containing:
        - List of task objects (id, task_type, description, status, due_date)
        - 200 status on success
        - Error message and 500 status on failure
    """
    try:
        # Fetch tasks from repository
        tasks = CaseTaskRepository.get_tasks_by_case_id(case_id)
        
        # Format response data
        response_data = [{
            "id": t.id,
            "task_type": t.task_type,
            "description": t.description,
            "status": t.status,
            "due_date": t.due_date.isoformat() if t.due_date else None
        } for t in tasks]
        
        return jsonify(response_data), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# ==============================================
# CASE BILLING API ENDPOINT
# ==============================================
@routes.route('/api/case-billing/<case_id>', methods=['GET'])
def get_case_billing(case_id):
    """Get comprehensive billing data for a case including:
    - All invoices
    - Standalone expenses
    - Calculated totals
    
    Args:
        case_id: The ID of the case to fetch billing data for
        
    Returns:
        JSON response containing:
        - success: Boolean indicating overall operation status
        - data: Object containing:
            - invoices: List of invoice objects
            - standalone_expenses: List of expense objects
            - totals: Calculated totals (invoiced, expenses, grand_total)
        - 200 status on success
        - Error details and 500 status on failure
    """
    try:
        # Initialize totals
        total_invoiced = 0.0
        total_expenses = 0.0
        has_critical_error = False
        error_details = []

        # --------------------------
        # INVOICES PROCESSING
        # --------------------------
        invoices = []
        try:
            invoice_rows = InvoiceRepository.get_invoices_by_case_id(case_id)
            if invoice_rows is None:
                raise Exception("Invoice query returned None")
                
            invoices = [InvoiceRepository.serialize_invoice(row) for row in invoice_rows]
            total_invoiced = sum(float(inv["total_amount"]) for inv in invoices if inv.get("total_amount"))
        except Exception as inv_error:
            current_app.logger.error(f"Invoice error for case {case_id}: {str(inv_error)}")
            error_details.append(f"Invoice error: {str(inv_error)}")
            # Only set critical error if invoices are essential
            has_critical_error = True

        # --------------------------
        # STANDALONE EXPENSES PROCESSING
        # --------------------------
        standalone_expenses = []
        try:
            expense_rows = ExpensesRepository.get_standalone_expenses_by_case_id(case_id)
            if expense_rows is None:
                raise Exception("Expenses query returned None")
                
            standalone_expenses = [ExpensesRepository.serialize_expense(exp) for exp in expense_rows]
            total_expenses = sum(float(exp["amount"]) for exp in standalone_expenses if exp.get("amount"))
        except Exception as exp_error:
            current_app.logger.error(f"Expenses error for case {case_id}: {str(exp_error)}")
            error_details.append(f"Expenses error: {str(exp_error)}")
            # Only set critical error if expenses are essential
            has_critical_error = True

        # If we have a critical error in essential components, return 500
        if has_critical_error and not invoices and not standalone_expenses:
            return jsonify({
                "success": False,
                "error": "Failed to fetch essential billing information",
                "details": error_details
            }), 500

        # --------------------------
        # DATA CLEANUP
        # --------------------------
        # Ensure all invoices have an invoice_number field
        for inv in invoices:
            if "invoice_number" not in inv:
                inv["invoice_number"] = inv.get("id", "N/A")
        
        # Ensure all expenses have a status field
        for exp in standalone_expenses:
            if "status" not in exp:
                exp["status"] = "N/A"

        # --------------------------
        # FINAL RESPONSE ASSEMBLY
        # --------------------------
        billing_data = {
            "invoices": invoices,
            "standalone_expenses": standalone_expenses,
            "totals": {
                "invoiced": total_invoiced,
                "expenses": total_expenses,
                "grand_total": total_invoiced + total_expenses
            }
        }

        response = {
            "success": True,
            "data": billing_data
        }
        
        # Add warnings if there were non-critical errors
        if error_details:
            response["warnings"] = error_details

        return jsonify(response), 200

    except Exception as e:
        current_app.logger.error(f"Billing error for case {case_id}: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Failed to fetch billing information",
            "details": str(e)
        }), 500
###############################
# Route for payout calculate
###############################
@routes.route('/api/payout/work-periods', methods=['GET'])
@roles_required('attorney', 'employee')
@employee_permission_required('calculate_payout')
def get_work_periods():
    try:
        context = util_filters.get_user_context()
        # Check if user is an attorney or employee
        representative = None
        if context["user_role"] == "attorney":
            representative = AttorneyRepository.get_attorney_by_id(context["id"])
        elif context["user_role"] == "employee":
            representative = EmployeeRepository.get_employee_by_id(context["id"])
        # Get params from request
        payload = validators.extract_payout_payload_from_request(request)
        # Validate payload
        validators.validate_payout_payload(payload)
        #Parse dates
        start_date, end_date = validators.parse_payload_dates(payload["start_str"], payload["end_str"])
        # Get workperiods from role and user_id
        work_periods, adjustments, pay_rate = get_user_payout_data(
            payload["user_role"], payload["user_id"], start_date, end_date, representative
        )
        # Calculate payroll summary
        payroll_result = calculate_payout_summary(payload["user_role"], work_periods, adjustments, pay_rate)
        
        if not payroll_result["work_periods"]:
            return jsonify({"error": "No work periods found on that range."}), 404  # Return 404 if no work periods found
        # Check for CSV export
        if payload["export"] == "csv":
            # Convert work_periods to CSV
            si = io.StringIO()
            writer = csv.DictWriter(si, fieldnames=payroll_result["work_periods"][0].keys())
            writer.writeheader()
            writer.writerows(payroll_result["work_periods"])
            output = si.getvalue()
            si.close()

            return Response(
                output,
                mimetype="text/csv",
                headers={"Content-Disposition": "attachment; filename=work_periods.csv"}
            )
        #Default JSON response
        return jsonify(payroll_result), 200

    except ValueError as ve:
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

#############################
#Stripe routes
#############################
@routes.route('/create-checkout-session/<int:invoice_id>', methods=['POST'])
@login_required
@roles_required('client')
def create_checkout_session(invoice_id):
    try:
        context = util_filters.get_user_context()
        user_id = context["id"]
        user_role = context["user_role"]

        if user_role != "client":
            raise PermissionError("Only clients can make payments")
        
        invoice = InvoiceRepository.get_invoice_by_id(invoice_id)
        if not invoice:
            raise ValueError("Invoice not found")
        if invoice.status == "paid":
            raise ValueError("Invoice already paid")
        case = CaseRepository.get_case_by_id(invoice.case_id)
        if not case:
            raise ValueError("Case not found")
        if case.client_id != user_id:
            raise PermissionError("This client is not assigned to this case")

        # Verify if the invoice is already in process for payment
        existing_session = CheckoutSessionRepository.get_checkout_session_by_invoice_id(invoice_id)
        if existing_session and invoice.status == "process_pending":
            stripe_session = stripe.checkout.Session.retrieve(existing_session.session_id)
            if stripe_session.status == "open":
                return jsonify({
                    "session": stripe_session.to_dict(),
                    "message": "Invoice is already in process for payment"
                }), 200


        data = request.json
        amount = invoice.amount * 100  # Convert to cents
        if not amount or amount <= 0:
            raise ValueError("Invoice has no amount or is 0")
        
        #currency = data.get("currency", "usd") future currency integration
        BillingInfo = BillingInfoRepository.get_billing_info_by_id(case.billing_info_id)

        payment_method = BillingInfo.billing_method if BillingInfo else None
        if payment_method == "credit_card":
            data["methods"] = ["card"] 
        elif payment_method == "ach":
            data["methods"] = ["us_bank_account"]
        elif payment_method == "usdc":
            data["methods"] = ["usdc"]
        else:
            data["methods"] = ["card", "us_bank_account", "usdc"]

        session = stripe.checkout.Session.create(
            payment_method_types=data["methods"],
            line_items=[{
                'price_data': {
                    'currency': "usd",
                    'product_data': {
                        'name': data.get("product_name", f"Payment for invoice #{invoice_id}"),
                    },
                    'unit_amount': int(amount),
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url='http://localhost:5000/',
            cancel_url='http://localhost:5000/',
            metadata={
                'invoice_id': invoice_id,
                'client_id': str(user_id),
                'case_id': case.case_id
            }
        )
        # Save session
        new_session = {
            "session_id": session.id,
            "invoice_id": invoice_id,
            "created_at": datetime.utcnow()
        }
        # Save session to the database
        CheckoutSessionRepository.create_checkout_session(new_session)
        # Update invoice status to "process_pending"
        InvoiceRepository.update_invoice_status(invoice_id,"process_pending")

        return jsonify(
            {
                "session":session.to_dict()
            }), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        db.session.rollback()
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()

        # Try to expire the session if it was created
        if 'session' in locals():
            try:
                stripe.checkout.Session.expire(session.id)
            except Exception as stripe_err:
                # Si expirar falla, al menos lo reportas
                print(f"Failed to expire session: {stripe_err}")

        return jsonify(error=str(e)), 500

# Webhook route for payment confirmation and expiration
@routes.route('/stripe/invoice_payments/webhook', methods=['POST'])
def stripe_webhook():
    
    invoice_id = None
    payload = request.data
    sig_header = request.headers.get('Stripe-Signature')
    endpoint_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
    try:
        try:
            event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        except ValueError as e:
            raise ValueError(f"Invalid payload: {str(e)}")
        except stripe.error.SignatureVerificationError as e:
            raise ValueError(f"Invalid signature: {str(e)}")

        event_type = event.get('type')
        session_data = event['data']['object']
        metadata = session_data.get('metadata', {})
        payment_type = metadata.get('type_payment')
        if payment_type:
            if payment_type == "trust_deposit":
                raise ValueError(f"Payment type {payment_type} not supported on this webhook")
        invoice_id = metadata.get('invoice_id')
        session_id = session_data.get('id')

        if not invoice_id:
            raise ValueError("Invoice ID not found in metadata")

        checkout_session = CheckoutSessionRepository.get_checkout_session_by_session_id(session_id)
        if not checkout_session:
            raise ValueError(f"Session {session_id} not found in database for invoice #{invoice_id}, not able to change payment status")
        
        if event_type == 'checkout.session.completed':
            InvoiceRepository.update_invoice_status(invoice_id, "paid")
            print(f"✅ Invoice {invoice_id} marked as 'paid'")
        
        elif event_type == 'checkout.session.expired':
            InvoiceRepository.update_invoice_status(invoice_id, "unpaid")
            print(f"⚠️ Invoice {invoice_id} marked as 'unpaid' due to expiration")
        
        CheckoutSessionRepository.delete_checkout_session_by_session_id(session_id)
        print(f"🗑️ Checkout session {session_id} deleted from DB")

        return jsonify(
            {
                'status': 'success',
                'message': f"Webhook processed for invoice {invoice_id}"
            }), 200
    except ValueError as ve:
        print(f"⚠️ Error processing webhook{f' for invoice {invoice_id}' if invoice_id else ''}: {str(ve)}")
        return 'Webhook processing error', 400

    except Exception as e:
        print(f"⚠️ Error processing webhook{f' for invoice {invoice_id}' if invoice_id else ''}: {str(ve)}")
        return 'Webhook processing error', 500


@routes.route('/sign-document')
def sign_document():
    return render_template('sign_document.html')


from repositories.signature_request import SignatureRequestRepository
print("🔥signature_request.py loaded and execute")
@routes.route('/api/signature-request', methods=['POST'])
@login_required
def create_signature_request():
    try:
        data = request.get_json()
        case_id = data.get('case_id')
        filename = data.get('filename')
        print(f"DEBUG: case_id: {case_id}, filename: {filename}")
        if not case_id or not filename:
            return jsonify({"error": "Missing case_id or filename"}), 400

        existing = SignatureRequestRepository.get_by_case_and_filename(case_id, filename)
        if existing:
            return jsonify({"error": "Signature request already exists"}), 400

        request_entry = SignatureRequestRepository.create(case_id, filename)
        return jsonify({"message": "Signature request created", "request_id": request_entry.id}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": "Failed to create signature request", "details": str(e)}), 500

#Stripe routes
#################################
@routes.route('/create-checkout-session/invoices/<int:invoice_id>', methods=['POST'])
@login_required
@roles_required('client')
def create_checkout_session_invoices(invoice_id):
    try:
        context = util_filters.get_user_context()
        user_id = context["id"]
        user_role = context["user_role"]

        if user_role != "client":
            raise PermissionError("Only clients can make payments")
        
        invoice = InvoiceRepository.get_invoice_by_id(invoice_id)
        if not invoice:
            raise ValueError("Invoice not found")
        if invoice.status == "process_pending":
            raise ValueError("Invoice already in process for paid")
        if invoice.status == "paid":
            raise ValueError("Invoice already paid")
        case = CaseRepository.get_case_by_id(invoice.case_id)
        if not case:
            raise ValueError("Case not found")
        if case.client_id != user_id:
            raise PermissionError("This client is not assigned to this case")

        data = request.json
        amount = invoice.amount * 100  # Convert to cents
        if not amount or amount <= 0:
            raise ValueError("Invoice has no amount or is 0")
        
        #currency = data.get("currency", "usd") future currency integration
        payment_method_types = data.get("methods", ["card", "us_bank_account"])

        session = stripe.checkout.Session.create(
            payment_method_types=payment_method_types,
            line_items=[{
                'price_data': {
                    'currency': "usd",
                    'product_data': {
                        'name': data.get("product_name", f"Payment for invoice #{invoice_id}"),
                    },
                    'unit_amount': int(amount),
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url='http://localhost:5000/',
            cancel_url='http://localhost:5000/',
            metadata={
                'invoice_id': invoice_id,
                'client_id': str(user_id),
                'case_id': case.case_id
            }
        )

        InvoiceRepository.update_invoice_status(invoice_id,"process_pending")

        return jsonify(session.to_dict()), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        db.session.rollback()
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify(error=str(e)), 500

@routes.route('/create-checkout-session/trust-deposit/<case_id>', methods=['POST'])
@login_required
@roles_required('client')
def create_checkout_session_trust_deposit(case_id):
    try:
        context = util_filters.get_user_context()
        user_id = context["id"]
        user_role = context["user_role"]

        if user_role != "client":
            raise PermissionError("Only clients can make payments")

        case = CaseRepository.get_case_by_id(case_id)
        if not case:
            raise ValueError("Case not found")
        if case.client_id != user_id:
            raise PermissionError("This client is not assigned to this case")

        data = request.json
        amount = data.amount * 100  # Convert to cents
        if not amount or amount <= 0:
            raise ValueError("Missing requiered field 'amount' or its 0")
        
        #currency = data.get("currency", "usd") future currency integration
        payment_method_types = data.get("methods", ["card", "us_bank_account"])

        session = stripe.checkout.Session.create(
            payment_method_types=payment_method_types,
            line_items=[{
                'price_data': {
                    'currency': "usd",
                    'product_data': {
                        'name': data.get("product_name", f"TrustDeposit for case #{case_id}"),
                    },
                    'unit_amount': int(amount),
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url='http://localhost:5000/',
            cancel_url='http://localhost:5000/',
            metadata={
                'type_payment': 'trust_deposit',
                'client_id': str(user_id),
                'case_id': case.case_id
            }
        )

        return jsonify(session.to_dict()), 200
    except ValueError as ve:
        db.session.rollback()
        return jsonify({"error": str(ve)}), 400
    except PermissionError as pe:
        db.session.rollback()
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify(error=str(e)), 500

# Webhooks route for payment confirmation on stripe
@routes.route('/webhook/trust-deposit', methods=['POST'])
def stripe_webhook_trust_deposit():
    payload = request.get_data(as_text=True)
    #sig_header = request.headers.get('Stripe-Signature') #future stripe integration

    #event = stripe.Webhook.construct_event(
    #   payload, sig_header, endpoint_secret
    #)

    event = json.loads(payload)

    if event['type'] == 'payment_intent.succeeded':
        payment = event['data']['object']
        case_id = payment['metadata']['case_id']
        amount = payment['amount_received'] / 100  # Stripe works with cents

        new_deposit = TrustDeposit(
            case_id=case_id,
            amount=amount,
            deposit_date=datetime.now(timezone.utc)
        )
        db.session.add(new_deposit)
        db.session.commit()

    return '', 200

@routes.route('/api/signature-requests', methods=['GET'])
@login_required
def get_signature_requests():
    try:
        case_id = request.args.get('case_id')
        if not case_id:
            return jsonify({"error": "Missing case_id"}), 400

        requests = SignatureRequestRepository.get_pending_by_case(case_id)
        return jsonify([{
            "id": r.id,
            "filename": r.filename,
            "status": r.status,
            "created_at": r.created_at.isoformat()
        } for r in requests]), 200

    except Exception as e:
        return jsonify({"error": "Failed to fetch signature requests", "details": str(e)}), 500
    
    
@routes.route('/api/signature-request/mark', methods=['POST'])
def mark_request_by_case_and_filename():
    try:
        data = request.get_json()
        case_id = data.get('case_id')
        filename = data.get('filename')

        updated = SignatureRequestRepository.mark_as_signed(case_id=case_id, filename=filename)
        if updated:
            return jsonify({"message": "Signature request marked as signed"}), 200
        return jsonify({"error": "No matching signature request found"}), 404

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": "Failed to update signature request", "details": str(e)}), 500
    
    
@routes.route('/case_management/uploads/<filename>')
def uploaded_file(filename):
    print(f"DEBUG: Serving file {filename}")
    return send_from_directory('uploads', filename)

@routes.route('/case-dashboard-summary', methods=['GET'])
@attorney_required  # Restricts access to authenticated attorneys only
def case_summary():
    """Renders the case dashboard summary view page."""
    return render_template('summary.html')

@routes.route('/api/case-dashboard-summary', methods=['GET'])
@attorney_required
def get_case_dashboard_summary():
    """
    Returns JSON data for dashboard with proper timezone handling
    """
    try:
        attorney_id = session.get('attorney_id')
        attorney_name = session.get('username')
        if not attorney_id:
            return jsonify({"error": "Attorney ID not found"}), 400

        # Initialize response data structure
        response_data = {
            "today_deadlines": [],
            "upcoming_deadlines": [],
            "pending_tasks": [],
            "recent_activities": [],
            "attorney_name": attorney_name
        }

        # Get current time with timezone awareness
        now = datetime.now(timezone.utc)
        today = now.date()
        one_week_ago = now - timedelta(days=7)

        # Get attorney's cases
        cases = CaseRepository.get_all_cases_by_attorney(attorney_id)
        if not cases:
            return jsonify(response_data)

        case_ids = [case.case_id for case in cases]
        case_name_map = {case.case_id: case.case_name for case in cases}

        # Helper function to make datetime timezone aware if it isn't
        def ensure_timezone_aware(dt):
            if dt is None:
                return None
            if dt.tzinfo is None:
                return dt.replace(tzinfo=timezone.utc)
            return dt

        # 1. Get today's deadlines
        start_of_day = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
        end_of_day = datetime.combine(today, datetime.max.time()).replace(tzinfo=timezone.utc)
        
        for case_id in case_ids:
            deadlines = CaseDeadlineRepository.get_deadlines_by_case_id(case_id)
            for d in deadlines:
                deadline_date = ensure_timezone_aware(d.deadline_date)
                if deadline_date and start_of_day <= deadline_date <= end_of_day:
                    response_data['today_deadlines'].append({
                        "id": d.id,
                        "case_id": case_id,
                        "case_name": case_name_map.get(case_id, ""),
                        "deadline_type": d.deadline_type,
                        "deadline_date": deadline_date.isoformat(),
                        "notes": d.notes or ""
                    })

        # 2. Get upcoming deadlines (next 7 days)
        next_week = end_of_day + timedelta(days=7)
        for case_id in case_ids:
            deadlines = CaseDeadlineRepository.get_deadlines_by_case_id(case_id)
            for d in deadlines:
                deadline_date = ensure_timezone_aware(d.deadline_date)
                if deadline_date and end_of_day < deadline_date <= next_week:
                    response_data['upcoming_deadlines'].append({
                        "id": d.id,
                        "case_id": case_id,
                        "case_name": case_name_map.get(case_id, ""),
                        "deadline_type": d.deadline_type,
                        "deadline_date": deadline_date.isoformat(),
                        "notes": d.notes or ""
                    })

        # 3. Get pending tasks
        for case_id in case_ids:
            tasks = CaseTaskRepository.get_tasks_by_case_id(case_id)
            for t in tasks:
                due_date = ensure_timezone_aware(t.due_date)
                if t.status == 'pending':
                    response_data['pending_tasks'].append({
                        "id": t.id,
                        "case_id": case_id,
                        "case_name": case_name_map.get(case_id, ""),
                        "task_type": t.task_type,
                        "description": t.description or "",
                        "due_date": due_date.isoformat() if due_date else None,
                        "priority": t.priority or "medium"
                    })

        # 4. Get recent activities
        for case_id in case_ids:
            activities = ActivityLogRepository.get_activities_by_case(case_id)
            for a in activities:
                activity_time = ensure_timezone_aware(a.timestamp)
                if activity_time and activity_time >= one_week_ago:
                    response_data['recent_activities'].append({
                        "id": a.id,
                        "case_id": case_id,
                        "case_name": case_name_map.get(case_id, ""),
                        "activity_type": a.activity_type,
                        "description": a.description or "",
                        "timestamp": activity_time.isoformat(),
                        "duration": float(a.time_spent) if a.time_spent else None
                    })

        # 5. Get recent messages
        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"Dashboard summary error: {str(e)}")
        traceback.print_exc()
        return jsonify({
            "error": "Failed to generate dashboard summary",
            "today_deadlines": [],
            "upcoming_deadlines": [],
            "pending_tasks": [],
            "recent_activities": [],
            "attorney_name": session.get('username', ''),
        }), 500


# ==============================================      
@routes.route('/mock-case-dashboard-summary', methods=['GET'])
def mock_case_dashboard_summary_page():
    """Mock view for Cypress testing without login or DB."""
    return render_template('summary.html')  # Same as the real one

@routes.route('/mock-api/case-dashboard-summary', methods=['GET'])
def mock_case_dashboard_summary_api():
    """Mock API response for Cypress."""
    return jsonify({
        "attorney_name": "John Attorney",
        "today_deadlines": [
            {
                "id": 1,
                "case_id": 101,
                "case_name": "Doe vs. Smith",
                "deadline_type": "Court Filing",
                "deadline_date": "2025-05-03T12:00:00Z",
                "notes": "File brief"
            }
        ],
        "upcoming_deadlines": [],
        "pending_tasks": [
            {
                "id": 12,
                "case_id": 101,
                "case_name": "Doe vs. Smith",
                "task_type": "Discovery",
                "description": "Send interrogatories",
                "due_date": "2025-05-07T10:00:00Z",
                "priority": "high"
            }
        ],
        "recent_activities": [
            {
                "id": 5,
                "case_id": 101,
                "case_name": "Doe vs. Smith",
                "activity_type": "Note",
                "description": "Call with client",
                "timestamp": "2025-05-02T15:30:00Z",
                "duration": 1.0
            }
        ]
    })
# ==============================================

# nofification view
@routes.route('/api/notifications', methods=['GET'])
@roles_required('attorney', 'client')   
def get_notifications():
    try:
        # Fix: Use user_id from session, fallback to attorney_id/client_id for backward compatibility
        user_id = session.get('user_id') or session.get('attorney_id') or session.get('client_id')
        user_role = session.get('user_role')

        if not user_id or not user_role:
            return jsonify({"error": "User not authenticated"}), 401
        
        # Add a simple test to see if we can return empty notifications first
        try:
            notifications = NotificationRepository.get_unread_notifications(user_id, user_role)
        except Exception as repo_error:
            # Return empty array to prevent 401, but log the repository error
            notifications = []

        notifications_data = [{
            "id": n.id,
            "message": n.message,
            "type": n.notification_type,
            "related_id": n.related_id,
            "created_at": n.created_at.isoformat(),
            "is_read": n.is_read
        } for n in notifications] if notifications else []
        
        return jsonify(notifications_data), 200

    except Exception as e:
        # Return empty array instead of error to prevent 401
        return jsonify([]), 200


@routes.route('/api/notifications/mark-read', methods=['POST'])
@roles_required('attorney', 'client')
def mark_notifications_read():
    try:
        user_id = session.get('attorney_id') or session.get('client_id')
        user_role = session.get('user_role')

        if not user_id or not user_role:
            return jsonify({"error": "User not authenticated"}), 401

        data = request.get_json()
        notification_ids = data.get('notification_ids', [])

        NotificationRepository.mark_notifications_read(user_id, user_role, notification_ids or None)

        return jsonify({"message": "Notifications marked as read"}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500
    except PermissionError as pe:
        return jsonify({"error": str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500
    
###########################
# Trust Deposit routes
###########################
@routes.route('/api/trust-deposits', methods=['POST'])
@login_required
@roles_required('employee', 'attorney')
@employee_permission_required('create_trust_deposit')
def create_trust_deposit():
    try:
        data = request.get_json()
        context = util_filters.get_user_context()
        
        case = CaseRepository.get_case_by_id(data["case_id"])
        if not case:
            raise ValueError('case not found')
        if not data or 'amount' not in data:
            raise ValueError('Missing required fields: amount')

        amount = data['amount']
        if amount <= 0:
            raise ValueError('Amount cannot be zeor or negative.')
        
        case_object = CaseRepository.get_case_by_id(data["case_id"])
        client = ClientRepository.get_client_by_id(case_object.client_id)

        representative = None
        if context["user_role"] == "attorney":
            representative = AttorneyRepository.get_attorney_by_id(context["id"])
            if case_object.attorney_id != representative.attorney_id:
                raise PermissionError("You don't have permission to create a trust deposit for this case.")

        elif context["user_role"] == "employee":
            representative = EmployeeRepository.get_employee_by_id(context["id"])
            if client.law_firm_id != representative.law_firm_id:
                raise PermissionError("You don't have permission to create a trust deposit for cases related to another law firm.")
        else:
            raise PermissionError("You don't have permission to create a trust deposit for this case.")

        trust_deposit = TrustDepositRepository.get_trust_deposit_by_case_id(case_object.case_id)
        if trust_deposit:
            if trust_deposit != 0:
                trust_deposit.amount += amount
                db.session.commit()
                return jsonify({
                    'message': 'Trust Already exists, amount updated.',
                    'trust_deposit': {
                        'id': trust_deposit.id,
                        'client_id': trust_deposit.client_id,
                        'case_id': trust_deposit.case_id,
                        'amount': trust_deposit.amount,
                        'deposit_date': trust_deposit.deposit_date.isoformat() if trust_deposit.deposit_date else None
                    }
                }), 201
            elif trust_deposit == 0:
                trust_deposit.amount += amount
                db.session.commit()
                return jsonify({
                    'message': 'Trust deposit created successfully.',
                    'trust_deposit': {
                        'id': trust_deposit.id,
                        'client_id': trust_deposit.client_id,
                        'case_id': trust_deposit.case_id,
                        'amount': trust_deposit.amount,
                        'deposit_date': trust_deposit.deposit_date.isoformat() if trust_deposit.deposit_date else None
                    }
                }), 201

        new_deposit = {
            "client_id": case_object.client_id,
            "case_id": data["case_id"],
            "amount": data['amount'],
            "deposit_date": datetime.now(timezone.utc)
        }

        trust_deposit = TrustDepositRepository.create_trust_deposit(new_deposit)
        if not trust_deposit:
            raise ValueError('Failed to create trust deposit.')
        
        return jsonify({
            'message': 'Trust deposit created successfully.',
            'trust_deposit': {
                'id': trust_deposit.id,
                'client_id': trust_deposit.client_id,
                'case_id': trust_deposit.case_id,
                'amount': trust_deposit.amount,
                'deposit_date': trust_deposit.deposit_date.isoformat() if trust_deposit.deposit_date else None
            }
        }), 201
    except ValueError as ve:
        return jsonify({'error': str(ve)}), 400
    except PermissionError as pe:
        return jsonify({'error': str(pe)}), 403
    except Exception as e:
        db.session.rollback()
        return jsonify({'error' : str(e)}), 500




@routes.route('/admin/permissions', methods=['GET'])
def render_role_permission_ui():
    return render_template('role_permission_matrix.html')


# fetching all deadlines from attorney 

@routes.route('/api/attorney/deadlines', methods=['GET'])
@attorney_required
def get_attorney_deadlines():
    try:
        deadlines = CaseDeadlineRepository.get_deadlines_for_logged_in_attorney()
        
        result = [
            {
                "id": d.id,
                "case_id": d.case_id,
                "deadline_date": d.deadline_date.isoformat(),
                "deadline_type": d.deadline_type,
                "notes": d.notes
            } for d in deadlines
        ]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@routes.route('/api/attorney/all-documents', methods=['GET'])
@attorney_required
def get_all_documents_for_attorney():
    try:
        # Fix: Use user_id from session, fallback to attorney_id for backward compatibility
        attorney_id = session.get('user_id') or session.get('attorney_id')
        
        if not attorney_id:
            return jsonify([]), 200  # Return empty array instead of error object
        
        cases = CaseRepository.get_all_cases_by_attorney(attorney_id)
        
        case_ids = [case.case_id for case in cases] if cases else []
        
        documents = DocumentRepository.get_documents_by_case_ids(case_ids) if case_ids else []
        
        # Always return an array, even if empty
        result = [
            {
                "id": doc.id,
                'case_id': doc.case_id,
                'original_name': doc.original_name,
                'document_type': doc.document_type,
                'uploaded_at': doc.uploaded_at.isoformat() if doc.uploaded_at else None
            } for doc in documents
        ]
        return jsonify(result)
    except Exception as e:
        # Return empty array on error to maintain frontend expectations
        # Log the error for debugging
        current_app.logger.error(f"Error fetching documents: {str(e)}")
        return jsonify([]), 200


# getting the deadlines cases of the logged in client

@routes.route('/api/client/deadlines', methods=['GET'])
def get_all_client_deadlines():
    # ✅ Step 1: Ensure the user is logged in and is a client
    if 'username' not in session or session.get('user_role') != 'client':
        return jsonify({'error': 'Unauthorized'}), 401

    client_id = session.get('client_id')

    # ✅ Step 2: Get all cases for the client
    client_cases = CaseRepository.get_cases_by_client_id(client_id)

    # ✅ Step 3: For each case, get deadlines
    all_deadlines = []
    for case in client_cases:
        deadlines = CaseDeadlineRepository.get_deadlines_for_case(case.case_id)

        for deadline in deadlines:
            deadline_data = {
                'id': deadline.id,
                'deadline_type': deadline.deadline_type,
                'deadline_date': deadline.deadline_date.isoformat() if deadline.deadline_date else None,
                'case_id': deadline.case_id,
                'case_name': case.case_name,
                'notes': deadline.notes or ""
            }
            all_deadlines.append(deadline_data)

    return jsonify({'upcoming_deadlines': all_deadlines}), 200

# fetching the law-firm name for the client

@routes.route('/api/client/firm-name', methods=['GET'])
def get_firm_name_for_client():
    if 'username' not in session or session.get('user_role') != 'client':
        return jsonify({"firm_name": "Your Law Firm"}), 401

    client_id = session.get('client_id')  # this is the string 'C32C1BFF2'
    client = db.session.query(Client).filter_by(client_id=client_id).first()

    if not client or not client.law_firm:
        return jsonify({"firm_name": "Your Law Firm"}), 404

    return jsonify({"firm_name": client.law_firm.name}), 200

# fetching pending task for logged in client

@routes.route('/api/client/pending-tasks', methods=['GET'])
def get_all_client_pending_tasks():
    # ✅ Step 1: Ensure the user is logged in and is a client
    if 'username' not in session or session.get('user_role') != 'client':
        return jsonify({'error': 'Unauthorized'}), 401

    client_id = session.get('client_id')

    # ✅ Step 2: Get all cases for the client
    client_cases = CaseRepository.get_cases_by_client_id(client_id)

    # ✅ Step 3: For each case, get pending tasks
    all_tasks = []
    for case in client_cases:
        tasks = CaseTaskRepository.get_tasks_by_case_id(case.case_id)

        for task in tasks:
            if task.status.lower() == "pending":
                task_data = {
                    'id': task.id,
                    'task_type': task.task_type,
                    'description': task.description or "",
                    'due_date': task.due_date.isoformat() if task.due_date else None,
                    'case_id': task.case_id,
                    'case_name': case.case_name
                }
                all_tasks.append(task_data)

    return jsonify({'pending_tasks': all_tasks}), 200


###################################
# Support Center Routes
###################################

@routes.route('/support', methods=['GET'])
@roles_required('attorney', 'client', 'employee')
def support_center():
    return render_template('support_center.html')

@routes.route('/api/support/tickets', methods=['GET', 'POST'])
@roles_required('attorney', 'client', 'employee')
def handle_tickets():
    if request.method == 'GET':
        # Get user's tickets
        user_id = session.get('attorney_id') or session.get('client_id') or session.get('employee_id')
        user_type = session.get('user_role')
        
        tickets = SupportRepository.get_user_tickets(user_id, user_type)
        tickets_data = [{
            'id': t.id,
            'priority': t.priority,
            'description': t.description,
            'status': t.status,
            'name': t.name,
            'email': t.email,
            'created_at': t.created_at.isoformat(),
            'updated_at': t.updated_at.isoformat()
        } for t in tickets]
        
        return jsonify(tickets_data), 200
    
    elif request.method == 'POST':
        # Create new ticket
        data = request.get_json()
        user_id = session.get('attorney_id') or session.get('client_id') or session.get('employee_id')
        user_type = session.get('user_role')
        law_firm_id = session.get('law_firm_id')
        
        # Get user info based on type
        if user_type == 'attorney':
            user = Attorney.query.filter_by(attorney_id=user_id).first()
        elif user_type == 'client':
            user = Client.query.filter_by(client_id=user_id).first()
        else:  # employee
            user = Employee.query.get(user_id)
        
        ticket = SupportRepository.create_ticket(
            user_id=user_id,
            user_type=user_type,
            name=user.name,
            email=user.email,
            firm_name=user.law_firm.name if hasattr(user, 'law_firm') else None,
            priority=data.get('priority'),
            description=data.get('description'),
            law_firm_id=law_firm_id
        )
        
        return jsonify({
            'id': ticket.id,
            'message': 'Ticket created successfully'
        }), 201

@routes.route('/api/support/tickets/<int:ticket_id>', methods=['GET'])
@roles_required('attorney', 'client', 'employee')
def get_ticket(ticket_id):
    ticket = SupportTicket.query.get(ticket_id)
    if not ticket:
        return jsonify({'error': 'Ticket not found'}), 404
        
    # Verify user owns this ticket
    user_id = session.get('attorney_id') or session.get('client_id') or session.get('employee_id')
    if ticket.user_id != user_id:
        return jsonify({'error': 'Unauthorized'}), 403
    
    return jsonify({
        'id': ticket.id,
        'priority': ticket.priority,
        'description': ticket.description,
        'status': ticket.status,
        'name': ticket.name,
        'email': ticket.email,
        'created_at': ticket.created_at.isoformat(),
        'updated_at': ticket.updated_at.isoformat()
    }), 200

@routes.route('/api/support/feedback/pre-ticket', methods=['POST'])
@roles_required('attorney', 'client', 'employee')
def submit_pre_ticket_feedback():
    data = request.get_json()
    user_id = session.get('attorney_id') or session.get('client_id') or session.get('employee_id')
    user_type = session.get('user_role')
    law_firm_id = session.get('law_firm_id')
    
    feedback = SupportRepository.create_feedback(
        user_id=user_id,
        user_type=user_type,
        feedback_type='pre_ticket',
        rating=data.get('rating'),
        comment=data.get('comment'),
        law_firm_id=law_firm_id
    )
    
    return jsonify({
        'id': feedback.id,
        'message': 'Feedback submitted successfully'
    }), 201

@routes.route('/api/support/feedback/post-resolution', methods=['POST'])
@roles_required('attorney', 'client', 'employee')
def submit_post_resolution_feedback():
    data = request.get_json()
    user_id = session.get('attorney_id') or session.get('client_id') or session.get('employee_id')
    user_type = session.get('user_role')
    law_firm_id = session.get('law_firm_id')
    
    feedback = SupportRepository.create_feedback(
        user_id=user_id,
        user_type=user_type,
        feedback_type='post_resolution',
        rating=data.get('rating'),
        comment=data.get('comment'),
        ticket_id=data.get('ticket_id'),
        law_firm_id=law_firm_id
    )
    
    return jsonify({
        'id': feedback.id,
        'message': 'Feedback submitted successfully'
    }), 201

@routes.route('/api/support/tickets/<int:ticket_id>/status', methods=['PUT'])
@roles_required('admin', 'employee')  # Only admin/employee can update status
def update_ticket_status(ticket_id):
    data = request.get_json()
    new_status = data.get('status')
    
    if new_status not in ['Open', 'In Progress', 'Resolved']:
        return jsonify({'error': 'Invalid status'}), 400
    
    ticket = SupportRepository.update_ticket_status(ticket_id, new_status)
    if not ticket:
        return jsonify({'error': 'Ticket not found'}), 404
    
    return jsonify({
        'id': ticket.id,
        'message': f'Ticket status updated to {new_status}'
    }), 200
    
    
@routes.route('/api/user-info', methods=['GET'])
@roles_required('attorney', 'client', 'employee')
def get_user_info():
    try:
        user_id = session.get('user_id')  # This is the database ID
        user_role = session.get('user_role')
        public_id = None  # Will store client_id/attorney_id/etc
        
        if not user_id or not user_role:
            return jsonify({'error': 'Missing session data'}), 400

        # Get the public ID based on user type
        if user_role == 'client':
            public_id = session.get('client_id')
        elif user_role == 'attorney':
            public_id = session.get('attorney_id')
        elif user_role == 'employee':
            public_id = session.get('employee_id')

        user = None
        if user_role == 'client':
            user = Client.query.filter_by(id=user_id).first()
        elif user_role == 'attorney':
            user = Attorney.query.filter_by(id=user_id).first()
        elif user_role == 'employee':
            user = Employee.query.filter_by(id=user_id).first()

        if not user:
            current_app.logger.error(f"User not found - ID: {user_id}, Role: {user_role}")
            return jsonify({'error': 'User not found'}), 404

        # Get firm info
        law_firm = None
        if hasattr(user, 'law_firm'):
            law_firm = user.law_firm
        elif hasattr(user, 'law_firm_id'):
            law_firm = LawFirm.query.get(user.law_firm_id)

        return jsonify({
            'name': user.name,
            'email': user.email,
            'firm_name': law_firm.name if law_firm else None,
            'user_type': user_role,
            'public_id': public_id  # Include this if needed elsewhere
        })

    except Exception as e:
        current_app.logger.error(f"Error in /api/user-info: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
    
@routes.route('/api/support/feedback/check', methods=['GET'])
@roles_required('attorney', 'client', 'employee')
def check_feedback():
    ticket_id = request.args.get('ticket_id')
    if not ticket_id:
        return jsonify({'error': 'Ticket ID required'}), 400
    
    feedback = Feedback.query.filter_by(
        ticket_id=ticket_id,
        feedback_type='post_resolution'
    ).first()
    
    return jsonify({
        'feedback_exists': feedback is not None
    })

# # # ==============================================
# # # TIME TRACKER ROUTES
# # # ==============================================
@routes.route('/time-entries')
@roles_required('attorney', 'employee', 'admin')
def time_entries():
    return render_template('time_entries.html')


@routes.route('/api/time-entries', methods=['GET', 'POST'])
@attorney_required
def handle_time_entries():
    if request.method == 'POST':
        try:
            current_user = get_current_user_info()
            if not current_user:
                return jsonify({'error': 'Not authenticated'}), 401

            data = request.get_json()
            required = ['date', 'case_id', 'description', 'duration', 'billed_by_type', 'billed_by_id', 'billing_rate']
            if not all(field in data for field in required):
                return jsonify({
                    'success': False,
                    'error': 'Missing required fields',
                    'missing': [field for field in required if field not in data]
                }), 400

            if float(data['duration']) <= 0:
                return jsonify({'success': False, 'error': 'Duration must be positive'}), 400

            entry = TimeEntry(
                date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
                case_id=data['case_id'],
                created_by_id=current_user['id'],
                created_by_type=current_user['type'],
                description=data['description'],
                duration=float(data['duration']),
                billing_rate=float(data['billing_rate']),
                is_billable=data.get('is_billable', True),
                tags=data.get('tags'),
                entry_method=data.get('entry_method', 'manual')
            )

            if data['billed_by_type'] == 'attorney':
                if not TimeEntryRepository.get_attorney_by_id(data['billed_by_id']):
                    return jsonify({'success': False, 'error': 'Attorney not found'}), 404
                entry.billed_by_attorney_id = data['billed_by_id']
            else:
                if not TimeEntryRepository.get_employee_by_id(data['billed_by_id']):
                    return jsonify({'success': False, 'error': 'Employee not found'}), 404
                entry.billed_by_employee_id = data['billed_by_id']

            TimeEntryRepository.create_time_entry(entry)

            created_by_info = get_user_display_info(entry.created_by_type, entry.created_by_id)

            return jsonify({
                'success': True,
                'message': 'Entry created',
                'id': entry.id,
                'date': entry.date.isoformat(),
                'case_id': entry.case_id,
                'description': entry.description,
                'duration': float(entry.duration),
                'billed_by': entry.billed_by_attorney.name if entry.billed_by_attorney else (entry.billed_by_employee.name if entry.billed_by_employee else None),                'billing_rate': float(entry.billing_rate),
                'is_billable': entry.is_billable,
                'tags': entry.tags,
                'sync_status': entry.sync_status,
                'created_by': created_by_info,
                'updated_by': None
            }), 201

        except ValueError as e:
            return jsonify({'success': False, 'error': 'Invalid data format', 'details': str(e)}), 400
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'error': 'Server error', 'details': str(e)}), 500

    # GET - List entries
    case_id = request.args.get('case_id')
    date = request.args.get('date')

    entries = TimeEntryRepository.get_entries(case_id=case_id, date=date)
    
    return jsonify([{
        'id': e.id,
        'date': e.date.isoformat(),
        'case_id': e.case_id,
        'description': e.description,
        'duration': float(e.duration),
        'billed_by': e.billed_by_attorney.name if e.billed_by_attorney else e.billed_by_employee.name,
        'billing_rate': float(e.billing_rate),
        'is_billable': e.is_billable,
        'tags': e.tags,
        'sync_status': e.sync_status,
        'created_by': get_user_display_info(e.created_by_type, e.created_by_id),
        'updated_by': get_user_display_info(e.updated_by_type, e.updated_by_id) if e.updated_by_id else None
    } for e in entries])
    
    
@routes.route('/api/billable-personnel')
def get_billable_personnel():
    attorneys = TimeEntryRepository.get_all_attorneys()
    employees = TimeEntryRepository.get_hourly_employees()

    personnel = []
    
    for a in attorneys:
        personnel.append({
            'type': 'attorney',
            'id': a.id,
            'name': f"{a.name} (Attorney)",
            'rate': float(a.hourly_rate)
        })
    
    for e in employees:
        personnel.append({
            'type': 'employee',
            'id': e.id,
            'name': f"{e.name} ({e.role})",
            'rate': float(e.hourly_rate)
        })
    
    return jsonify(sorted(personnel, key=lambda x: x['name']))

@routes.route('/api/time-entries/import', methods=['POST'])
@roles_required('attorney', 'employee')
def import_entries():
    try:
        current_user = get_current_user_info()
        if not current_user:
            return jsonify({'error': 'Not authenticated'}), 401

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Normalize input to list
        if not isinstance(data, list):
            data = [data]

        # Add created_by info to each entry
        for entry in data:
            entry['created_by_id'] = current_user['id']
            entry['created_by_type'] = current_user['type']

        results = TimeEntryRepository.bulk_import_entries(data)
        return jsonify(results)

    except Exception as e:
        import traceback
        traceback.print_exc()  # This will print the full traceback to console
        return jsonify({'error': str(e), 'trace': traceback.format_exc()}), 500
    
    
@routes.route('/api/time-entries/<int:entry_id>', methods=['DELETE'])
@roles_required('attorney', 'employee', 'admin')
def delete_time_entry(entry_id):
    try:
        entry = TimeEntryRepository.delete_entry(entry_id)
        if not entry:
            return jsonify({'success': False, 'error': 'Entry not found'}), 404

        return jsonify({'success': True, 'message': 'Entry deleted'})

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Server error',
            'details': str(e)
        }), 500
        
        
@routes.route('/api/time-entries/<int:entry_id>', methods=['GET'])
def get_time_entry(entry_id):
    entry = TimeEntryRepository.get_entry_by_id(entry_id)
    if not entry:
        return jsonify({'success': False, 'error': 'Entry not found'}), 404

    created_by = get_user_display_info(entry.created_by_type, entry.created_by_id) if entry.created_by_id else None
    updated_by = get_user_display_info(entry.updated_by_type, entry.updated_by_id) if entry.updated_by_id else None

    return jsonify({
        'success': True,
        'entry': {
            'id': entry.id,
            'date': entry.date.isoformat(),
            'case_id': entry.case_id,
            'description': entry.description,
            'duration': float(entry.duration),
            'billed_by_type': 'attorney' if entry.billed_by_attorney_id else 'employee',
            'billed_by_id': entry.billed_by_attorney_id or entry.billed_by_employee_id,
            'billing_rate': float(entry.billing_rate),
            'is_billable': entry.is_billable,
            'tags': entry.tags,
            'sync_status': entry.sync_status,
            'created_by': created_by,
            'updated_by': updated_by,
            'created_at': entry.created_at.isoformat() if entry.created_at else None,
            'updated_at': entry.updated_at.isoformat() if entry.updated_at else None
        }
    })

@routes.route('/api/time-entries/<int:entry_id>', methods=['PUT'])
@roles_required('attorney', 'employee', 'admin')
def update_time_entry(entry_id):
    current_user = get_current_user_info()
    if not current_user:
        return jsonify({'error': 'Not authenticated'}), 401

    data = request.get_json()
    required = ['date', 'case_id', 'description', 'duration', 'billed_by_type', 'billed_by_id', 'billing_rate']
    missing_fields = [field for field in required if field not in data]
    if missing_fields:
        return jsonify({'success': False, 'error': 'Missing required fields', 'missing': missing_fields}), 400

    entry, error = TimeEntryRepository.update_entry(entry_id, data, current_user)
    if error == 'Entry not found':
        return jsonify({'success': False, 'error': error}), 404
    elif error:
        return jsonify({'success': False, 'error': 'Server error', 'details': error}), 500

    created_by_info = get_user_display_info(entry.created_by_type, entry.created_by_id)
    updated_by_info = get_user_display_info(entry.updated_by_type, entry.updated_by_id)

    return jsonify({
        'success': True,
        'message': 'Entry updated',
        'entry': {
            'id': entry.id,
            'date': entry.date.isoformat(),
            'case_id': entry.case_id,
            'description': entry.description,
            'duration': float(entry.duration),
            'billed_by': entry.billed_by_attorney.name if entry.billed_by_attorney else entry.billed_by_employee.name,
            'billing_rate': float(entry.billing_rate),
            'is_billable': entry.is_billable,
            'tags': entry.tags,
            'sync_status': entry.sync_status,
            'created_by': created_by_info,
            'updated_by': updated_by_info,
            'created_at': entry.created_at.isoformat() if entry.created_at else None,
            'updated_at': entry.updated_at.isoformat() if entry.updated_at else None
        }
    })

@routes.route('/api/law-firm/by-name/<string:law_firm_name>', methods=['GET'])
def get_law_firm_by_name(law_firm_name):
    from repositories.lawFirm import LawFirmRepository  # Fixed import case
    law_firm = LawFirmRepository.get_law_firm_by_name(law_firm_name)
    if not law_firm:
        return jsonify({'error': 'Law firm not found'}), 404
    return jsonify({
        'id': law_firm.id,
        'name': law_firm.name,
        'address': law_firm.address,
        'contact_email': law_firm.contact_email,
        'phone_number': law_firm.phone_number
    })

@routes.route('/mobile/client/<string:client_id>/dashboard', methods=['GET'])
def mobile_client_dashboard(client_id):
    try:
        # Get cases
        cases = CaseRepository.get_cases_by_client_id(client_id)
        cases_data = [{
            "case_id": case.case_id,
            "case_name": case.case_name,
            "case_status": case.case_status
        } for case in cases]

        # Get documents (first 5 recent ones)
        documents = []
        for case in cases[:5]:  # Limit to 5 cases to avoid too many documents
            docs = DocumentRepository.get_documents_by_case_id(case.case_id)
            for doc in docs[:5]:  # Limit to 5 documents per case
                documents.append({
                    "id": doc.id,
                    "case_id": case.case_id,
                    "case_name": case.case_name,
                    "original_name": doc.original_name,
                    "document_type": doc.document_type,
                    "uploaded_at": doc.uploaded_at.isoformat() if doc.uploaded_at else None
                })

        # Get deadlines
        deadlines = []
        client_cases = CaseRepository.get_cases_by_client_id(client_id)
        for case in client_cases:
            case_deadlines = CaseDeadlineRepository.get_deadlines_for_case(case.case_id)
            for deadline in case_deadlines:
                deadlines.append({
                    'id': deadline.id,
                    'deadline_type': deadline.deadline_type,
                    'deadline_date': deadline.deadline_date.isoformat() if deadline.deadline_date else None,
                    'case_id': deadline.case_id,
                    'case_name': case.case_name,
                    'notes': deadline.notes or ""
                })

        return jsonify({
            "success": True,
            "cases": cases_data,
            "documents": documents,
            "deadlines": deadlines
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500        

# Only one Support Center Routes block should be present below. Remove any duplicates.
###################################
# Support Center Routes
###################################

from repositories.support_repository import SupportRepository
from case_management.models import SupportTicket, Attorney, Client, Employee, LawFirm,Feedback

# Add these routes to your existing routes.py

def safe_iso(dt):
    if isinstance(dt, (datetime, date)):
        try:
            return dt.isoformat()
        except Exception:
            return None
    return None

    
    
@routes.route('/api/document/<int:document_id>/audit-logs/export', methods=['GET'])
@attorney_required
def export_document_audit_logs(document_id):
    try:
        # Verify document exists and belongs to attorney's cases
        document = DocumentRepository.get_document_by_id(document_id)
        if not document:
            return jsonify({"error": "Document not found"}), 404

        # Get all audit logs for this document
        logs = DocumentAuditLog.query.filter_by(document_id=document_id)\
            .order_by(DocumentAuditLog.timestamp.desc())\
            .all()

        # Create CSV output
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Timestamp', 'Action', 'User Type', 'User ID', 
            'IP Address', 'Version', 'Details'
        ])

        # Write rows
        for log in logs:
            writer.writerow([
                log.timestamp.isoformat(),
                log.action,
                log.user_type,
                log.user_id,
                log.ip_address or '',
                log.version_number or '',
                json.dumps(log.details) if log.details else ''
            ])

        # Prepare response
        output.seek(0)
        return Response(
            output,
            mimetype="text/csv",
            headers={
                "Content-Disposition": f"attachment;filename=document_{document_id}_audit_logs.csv"
            }
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

# this routes is only for testing purpose 

@routes.route("/test-log-action")
def test_log_action_route():
    # Fake session data for test
    session['user_role'] = 'attorney'
    session['attorney_id'] = 1
    session['username'] = 'test_attorney'

    log_document_action(
        document_id=1,
        action="view",
        request=request,
        version_number=1,
        details={"test": "data"}
    )
    return "Logged", 200

@routes.route('/api/contacts/<int:contact_id>', methods=['PUT'])
def update_contact(contact_id):
    """
    Update a contact (attorney, employee, or external) by id. Accepts JSON body with fields to update (name, email, phone).
    """
    user_role = session.get('user_role')
    if user_role not in ['attorney', 'admin']:
        return jsonify({"error": "Unauthorized"}), 401

    data = request.get_json() or {}
    name = data.get('name')
    email = data.get('email')
    phone = data.get('phone')
    contact_type = data.get('contact_type')  # To identify what type of contact

    # Try to find attorney first
    from repositories.attorney import AttorneyRepository
    from repositories.employee import EmployeeRepository
    from case_management.models import Attorney, Employee, ExternalContact
    
    obj = Attorney.query.filter_by(id=contact_id).first()
    if obj:
        if name: obj.name = name
        if email: obj.email = email
        if phone: obj.phone_number = phone
        try:
            from case_management.extensions import db
            db.session.commit()
            return jsonify({"message": "Attorney contact updated successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    # Try employee
    obj = Employee.query.filter_by(id=contact_id).first()
    if obj:
        if name: obj.name = name
        if email: obj.email = email
        if phone: obj.phone_number = phone
        try:
            from case_management.extensions import db
            db.session.commit()
            return jsonify({"message": "Employee contact updated successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    # Try external contact
    obj = ExternalContact.query.filter_by(id=contact_id).first()
    if obj:
        if name: obj.name = name
        if email: obj.email = email
        if phone: obj.phone = phone
        try:
            from case_management.extensions import db
            db.session.commit()
            return jsonify({"message": "External contact updated successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    return jsonify({"error": "Contact not found"}), 404

@routes.route('/api/contacts/<int:contact_id>', methods=['DELETE'])
def delete_contact(contact_id):
    """
    Delete a contact (attorney, employee, or external) by id. Only for attorney/admin roles.
    """
    user_role = session.get('user_role')
    if user_role not in ['attorney', 'admin']:
        return jsonify({"error": "Unauthorized"}), 401

    from repositories.attorney import AttorneyRepository
    from repositories.employee import EmployeeRepository
    from case_management.models import Attorney, Employee, ExternalContact
    from case_management.extensions import db

    # Try attorney first
    obj = Attorney.query.filter_by(id=contact_id).first()
    if obj:
        try:
            db.session.delete(obj)
            db.session.commit()
            return jsonify({"message": "Attorney contact deleted successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    # Try employee
    obj = Employee.query.filter_by(id=contact_id).first()
    if obj:
        try:
            db.session.delete(obj)
            db.session.commit()
            return jsonify({"message": "Employee contact deleted successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    # Try external contact
    obj = ExternalContact.query.filter_by(id=contact_id).first()
    if obj:
        try:
            db.session.delete(obj)
            db.session.commit()
            return jsonify({"message": "External contact deleted successfully"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    return jsonify({"error": "Contact not found"}), 404