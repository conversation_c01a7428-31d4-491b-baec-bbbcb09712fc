document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("employeeRegistrationForm");
  const messageContainer = document.getElementById("registration_message");

  form.addEventListener("submit", async function (e) {
    e.preventDefault();

    const formData = new FormData(form);
    let payload = {};
    formData.forEach((value, key) => {
      payload[key] = value;
    });

    try {
      if (payload.hourly_rate) {
        payload.hourly_rate = parseFloat(payload.hourly_rate);
      }

      const response = await fetch(form.action, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        messageContainer.textContent =
          result.message || "Employee registered successfully!";
        messageContainer.className = "success-message";

        // Show a confirmation modal with options
        showConfirmationModal(result.employee_id, payload.username);

        // Also provide automatic redirect as fallback after 10 seconds
        setTimeout(() => {
          // Check if modal still exists (user hasn't clicked anything)
          const existingModal = document.querySelector(".confirmation-modal");
          if (existingModal) {
            window.location.href = `/employee/dashboard?new_employee=${result.employee_id}`;
          }
        }, 10000);
      } else {
        let errorMessage =
          result.message || "An error occurred during registration.";
        if (response.status === 400) {
          if (errorMessage.includes("Missing required fields")) {
            errorMessage = "Please fill in all required fields.";
          } else if (errorMessage.includes("Email already exists")) {
            errorMessage = "This email is already registered.";
          }
        }
        messageContainer.textContent = errorMessage;
        messageContainer.className = "error-message";
      }
    } catch (error) {
      messageContainer.textContent = "Network error. Please try again.";
      messageContainer.className = "error-message";
      console.error("Error during employee registration:", error);
    }
  });

  function showConfirmationModal(employeeId, username) {
    const modal = document.createElement("div");
    modal.className = "confirmation-modal";
    modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

    modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                padding: 25px;
                border-radius: 8px;
                width: 90%;
                max-width: 400px;
                text-align: center;
                box-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
            ">
                <h3>Registration Successful!</h3>
                <p>What would you like to do next?</p>
                <p id="countdown" style="
                    font-size: 0.9em;
                    color: #666;
                    margin-top: 10px;
                ">Automatically redirecting to dashboard in <span id="countdown-timer">10</span> seconds...</p>
                <div class="modal-buttons" style="
                    display: flex;
                    justify-content: space-around;
                    margin-top: 25px;
                ">
                    <button id="goToDashboard" style="
                        padding: 10px 20px;
                        cursor: pointer;
                        border: none;
                        border-radius: 4px;
                        font-weight: 600;
                        background-color: #3498db;
                        color: white;
                    ">Go to Dashboard</button>
                    <button id="goToLogin" style="
                        padding: 10px 20px;
                        cursor: pointer;
                        border: none;
                        border-radius: 4px;
                        font-weight: 600;
                        background-color: #e74c3c;
                        color: white;
                    ">Go to Login</button>
                </div>
            </div>
        `;

    document.body.appendChild(modal);

    // Add hover effects
    const dashboardBtn = document.getElementById("goToDashboard");
    const loginBtn = document.getElementById("goToLogin");

    dashboardBtn.addEventListener("mouseenter", () => {
      dashboardBtn.style.backgroundColor = "#2980b9";
    });
    dashboardBtn.addEventListener("mouseleave", () => {
      dashboardBtn.style.backgroundColor = "#3498db";
    });

    loginBtn.addEventListener("mouseenter", () => {
      loginBtn.style.backgroundColor = "#c0392b";
    });
    loginBtn.addEventListener("mouseleave", () => {
      loginBtn.style.backgroundColor = "#e74c3c";
    });

    // Start countdown timer
    let countdown = 10;
    const countdownElement = document.getElementById("countdown-timer");
    const countdownInterval = setInterval(() => {
      countdown--;
      if (countdownElement) {
        countdownElement.textContent = countdown;
      }
      if (countdown <= 0) {
        clearInterval(countdownInterval);
      }
    }, 1000);

    dashboardBtn.addEventListener("click", () => {
      clearInterval(countdownInterval);
      // Redirect to dashboard with the new employee ID
      window.location.href = `/employee/dashboard?new_employee=${employeeId}`;
    });

    loginBtn.addEventListener("click", () => {
      clearInterval(countdownInterval);
      window.location.href = "/login?username=" + encodeURIComponent(username);
    });

    // Close modal when clicking outside
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        clearInterval(countdownInterval);
        document.body.removeChild(modal);
      }
    });
  }
});
