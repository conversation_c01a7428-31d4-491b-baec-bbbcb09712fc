document.addEventListener("DOMContentLoaded", () => {
  // Initialize socket.io
  const socket = io();

  // Set current date
  const options = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  };
  const today = new Date();
  document.getElementById("current-date").textContent =
    today.toLocaleDateString("en-US", options);

  // Tab switching
  const tabLinks = document.querySelectorAll(".tab-link");
  const tabContents = document.querySelectorAll(".tab-content");

  tabLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const targetTabId = e.target.getAttribute("data-tab");
      switchTab(targetTabId);
    });
  });

  // Initialize cases tab with overview button functionality
  initCasesTab();

  // Initial data load - force load overview data immediately
  loadInitialData();
  fetchDashboardData(); // Explicitly load overview data

  // Setup socket.io listeners
  setupSocketListeners(socket);

  // Setup event listeners for buttons
  setupEventListeners();

  initializeQuickActions();
});

/**
 * Switches between tabs and loads appropriate data
 * @param {string} targetTabId - The ID of the tab to switch to
 */
function switchTab(targetTabId) {
  const tabLinks = document.querySelectorAll(".tab-link");
  const tabContents = document.querySelectorAll(".tab-content");

  tabLinks.forEach((btn) => btn.classList.remove("active"));
  tabContents.forEach((tab) => tab.classList.remove("active"));

  // Highlight active tab in sidebar
  document.querySelectorAll(".sidebar-menu li").forEach((item) => {
    item.classList.remove("active");
    if (item.querySelector(`a[data-tab="${targetTabId}"]`)) {
      item.classList.add("active");
    }
  });

  document.getElementById(targetTabId).classList.add("active");

  // Load appropriate data
  if (targetTabId === "contacts-tab") {
    fetchContacts();
    // Clear contact search input when switching to contacts tab
    const contactSearchInput = document.getElementById("contact-search");
    if (contactSearchInput) {
      contactSearchInput.value = "";
    }
  } else if (targetTabId === "cases-tab") {
    fetchCases();
  } else if (targetTabId === "overview-tab") {
    fetchDashboardData(); // Ensure data is loaded when tab is clicked
  }
}

/**
 * Initializes the cases tab with click handlers
 */
function initCasesTab() {
  document.querySelectorAll(".case-item").forEach((item) => {
    const caseId = item.dataset.caseId;

    item.onclick = () => {
      window.location.href = `/case/${caseId}/details`;
    };

    // --- Overview Button ---
    const overviewBtn = document.createElement("button");
    overviewBtn.className = "btn btn-secondary btn-sm overview-btn";
    overviewBtn.innerHTML = '<i class="fas fa-chart-bar"></i> Overview';
    overviewBtn.onclick = (e) => {
      e.stopPropagation();
      window.location.href = `/case-overview?case_id=${caseId}&tab=activities`;
    };

    // --- Update Button ---
    const updateBtn = document.createElement("button");
    updateBtn.className = "btn btn-primary btn-sm update-case-btn ms-2";
    updateBtn.innerHTML = '<i class="fas fa-edit"></i> Update';
    updateBtn.onclick = (e) => {
      e.stopPropagation();
      window.location.href = `/update-case-form?case_id=${caseId}`;
    };

    const actionsDiv =
      item.querySelector(".case-actions") || document.createElement("div");
    actionsDiv.className = "case-actions";
    actionsDiv.appendChild(overviewBtn);
    actionsDiv.appendChild(updateBtn);
    item.appendChild(actionsDiv);
  });
}

/**
 * Loads initial data for the active tab
 */
function loadInitialData() {
  // Load clients
  console.log("🔄 Loading clients from /attorney/clients");
  fetch("/attorney/clients")
    .then((response) => {
      console.log("📡 Client fetch response status:", response.status);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then((clients) => {
      console.log("👥 Clients received:", clients);
      console.log("📊 Number of clients:", Array.isArray(clients) ? clients.length : 'Not an array');
      populateClients(clients);
      populateClientOverview(clients);
    })
    .catch((error) => {
      console.error("❌ Error fetching clients:", error);
      // Show error message to user
      const tableBody = document.getElementById("clients-list");
      if (tableBody) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="2" class="empty-state error">
              <i class="fas fa-exclamation-triangle"></i>
              <p>Error loading clients: ${error.message}</p>
              <button onclick="loadInitialData()" class="btn btn-sm btn-primary">Retry</button>
            </td>
          </tr>
        `;
      }
    });

  // Load contacts automatically like clients
  fetchContacts();

  // Load initial tab data
  const activeTab = document.querySelector(".tab-content.active").id;
  //   if (activeTab === "contacts-tab") {
  //     fetchContacts();
  //   } else
  if (activeTab === "cases-tab") {
    fetchCases();
  }
}

/**
 * Populates client overview statistics
 * @param {Array} clients - Array of client objects
 */
function populateClientOverview(clients) {
  const activeCount = Array.isArray(clients) ? clients.length : 0;
  document.querySelector("#client-overview .client-stats").innerHTML = `
        <div class="stat-item">
            <span class="stat-value">${activeCount}</span>
            <span class="stat-label">Active Clients</span>
        </div>
    `;
}

/**
 * Initializes quick action modals and forms
 */
function initializeQuickActions() {
  // Task form submission
  const taskForm = document.getElementById("taskForm");
  if (taskForm) {
    taskForm.addEventListener("submit", function (e) {
      e.preventDefault();
      createTask();
    });
  }

  // Deadline form submission
  const deadlineForm = document.getElementById("deadlineForm");
  if (deadlineForm) {
    deadlineForm.addEventListener("submit", function (e) {
      e.preventDefault();
      createDeadline();
    });
  }

  // Populate case dropdowns
  fetch("/api/get-all-cases-by-attorney")
    .then((response) => response.json())
    .then((cases) => {
      const caseSelects = document.querySelectorAll(
        "#taskCaseId, #deadlineCaseId"
      );
      caseSelects.forEach((select) => {
        // Clear existing options
        select.innerHTML = '<option value="">Select a case</option>';

        cases.forEach((c) => {
          const option = document.createElement("option");
          option.value = c.case_id;
          option.textContent = c.case_name || "Untitled Case";
          select.appendChild(option);
        });
      });
    })
    .catch((error) =>
      console.error("Error fetching cases for dropdown:", error)
    );
}

/**
 * Shows the quick action modal for different actions
 * @param {string} action - The action type (event, document, message, etc.)
 */
function showQuickActionModal(action) {
  const modal = document.getElementById("quickActionModal");
  const title = document.getElementById("quickActionTitle");
  const content = document.getElementById("quickActionContent");

  const actionTitles = {
    event: "Create Event",
    document: "Upload Document",
    message: "Send Message",
    time: "Log Time Entry",
    expense: "Add Expense",
    invoice: "Create Invoice",
    note: "Add Note",
  };

  title.innerHTML = `<i class="fas fa-${getIconForAction(action)}"></i> ${
    actionTitles[action]
  }`;
  content.innerHTML = `<p>${actionTitles[action]} form will appear here. This is a placeholder.</p>`;

  modal.style.display = "block";
}

/**
 * Shows the task creation modal
 */
function showTaskModal() {
  document.getElementById("taskModal").style.display = "block";
}

// Attach to the global `window` object
window.showTaskModal = showTaskModal;

/**
 * Shows the deadline creation modal
 */
function showDeadlineModal() {
  document.getElementById("deadlineModal").style.display = "block";
}

// Attach to the global `window` object
window.showDeadlineModal = showDeadlineModal;

/**
 * Closes a modal by ID
 * @param {string} modalId - The ID of the modal to close
 */
function closeModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = "none";
  } else {
    console.error(`Modal with ID "${modalId}" not found.`);
  }
}

// Attach to the global `window` object
window.closeModal = closeModal;

/**
 * Gets the appropriate icon for an action type
 * @param {string} action - The action type
 * @returns {string} The icon class name
 */
function getIconForAction(action) {
  const icons = {
    event: "calendar-check",
    document: "file-alt",
    message: "envelope",
    time: "stopwatch",
    expense: "receipt",
    invoice: "file-invoice-dollar",
    note: "sticky-note",
  };
  return icons[action] || "plus-circle";
}

/**
 * Creates a new task via API
 */
async function createTask() {
  const formData = {
    case_id: document.getElementById("taskCaseId").value,
    task_type: document.getElementById("taskType").value,
    description: document.getElementById("taskDescription").value,
    due_date: document.getElementById("taskDueDate").value,
    priority: document.getElementById("taskPriority").value,
    status: "pending",
  };

  try {
    const response = await fetch("/api/tasks", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    });

    const result = await response.json();

    if (response.ok) {
      showToast("Task created successfully!");
      closeModal("taskModal");
      document.getElementById("taskForm").reset();
      // Refresh relevant data
      if (document.querySelector(".tab-content.active").id === "cases-tab") {
        fetchCases();
      }
      fetchDashboardData();
    } else {
      showToast(`Error: ${result.error}`);
    }
  } catch (error) {
    showToast("Failed to create task: " + error.message);
  }
}

/**
 * Creates a new deadline via API
 */
async function createDeadline() {
  const formData = {
    case_id: document.getElementById("deadlineCaseId").value,
    deadline_type: document.getElementById("deadlineType").value,
    deadline_date: document.getElementById("deadlineDate").value,
    notes: document.getElementById("deadlineNotes").value,
    is_court_date: document.getElementById("isCourtDate").checked,
  };

  try {
    const response = await fetch("/api/deadlines", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    });

    const result = await response.json();

    if (response.ok) {
      showToast("Deadline added successfully!");
      closeModal("deadlineModal");
      document.getElementById("deadlineForm").reset();
      // Refresh relevant data
      fetchDeadlines();
      if (document.querySelector(".tab-content.active").id === "cases-tab") {
        fetchCases();
      }
    } else {
      showToast(`Error: ${result.error}`);
    }
  } catch (error) {
    showToast("Failed to add deadline: " + error.message);
  }
}

/**
 * Fetches and populates client data
 */
function populateClients(clients) {
  console.log("🏗️ populateClients called with:", clients);
  const tableBody = document.getElementById("clients-list");

  if (!tableBody) {
    console.error("❌ clients-list element not found!");
    return;
  }

  tableBody.innerHTML = "";

  if (!Array.isArray(clients) || clients.length === 0) {
    console.log("📭 No clients to display");
    tableBody.innerHTML = `
        <tr>
          <td colspan="2" class="empty-state">
            <i class="fas fa-users"></i>
            <p>No clients found</p>
          </td>
        </tr>
      `;
    return;
  }

  console.log(`📋 Displaying ${clients.length} clients`);

  clients.forEach((client) => {
    const row = document.createElement("tr");
    row.innerHTML = `
        <td>${client.name || "N/A"}
            <button class="btn btn-sm btn-primary update-client-btn" data-client-id="${
              client.client_id
            }">
                <i class="fas fa-edit"></i> Update
            </button>
        </td>
        <td>
            <button class="btn btn-sm btn-primary update-client-btn" data-client-id="${
              client.client_id
            }">
                <i class="fas fa-edit"></i> Update
            </button>
        </td>
      `;
    tableBody.appendChild(row);
  });

  // Attach click handlers to update buttons
  document.querySelectorAll(".update-client-btn").forEach((button) => {
    button.addEventListener("click", (e) => {
      const clientId = e.target.closest("button").dataset.clientId;
      window.location.href = `/update-client-form?client_id=${clientId}`;
    });
  });
}

/**
 * Fetches cases data from the server
 * @returns {Promise} Promise that resolves with cases data
 */
function fetchCases() {
  return fetch("/api/get-all-cases-by-attorney")
    .then((response) => response.json())
    .then((cases) => {
      renderCases(cases);
      return cases; // Return cases for chaining
    })
    .catch((error) => {
      console.error("Error fetching cases:", error);
      showError("cases-list", "Failed to load cases");
      return []; // Return empty array in case of error
    });
}

/**
 * Renders cases in the cases list
 * @param {Array} cases - Array of case objects
 */
function renderCases(cases) {
  const container = document.getElementById("cases-list");
  container.innerHTML = "";

  if (!cases || cases.length === 0) {
    container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>No cases found</p>
        </div>
      `;
    return;
  }

  cases.forEach((caseItem) => {
    const li = document.createElement("li");
    li.className = "case-item";
    li.dataset.caseId = caseItem.case_id;
    li.innerHTML = `
        <div class="case-header">
          <span class="case-name">${
            caseItem.case_name || "Untitled Case"
          }</span>
          <span class="case-status ${caseItem.case_status.toLowerCase()}">${
      caseItem.case_status
    }</span>
        </div>
        <div class="case-details">
          <span class="case-type">${caseItem.case_type}</span>
          <span class="client-name">${caseItem.client_name}</span>
        </div>
        <div class="case-actions"></div>
      `;
    container.appendChild(li);
  });

  // Reinitialize case buttons after rendering
  initCasesTab();
}

/**
 * Fetches contacts data from the server
 */
function fetchContacts() {
  fetch("/api/contacts")
    .then((response) => {
      return response.json();
    })
    .then((contacts) => {
      populateContactsTable(contacts);
    })
    .catch((err) => {
      console.error("Error fetching contacts:", err);
      showError("contacts-tab", "Failed to load contacts");
    });
}

/**
 * Filters contacts based on search query
 * @param {string} searchQuery - The search query to filter contacts
 */
function filterContacts(searchQuery) {
  if (!window._lastContactsList || !Array.isArray(window._lastContactsList)) {
    console.warn("No contacts data available for filtering");
    return;
  }

  // If search query is empty, show all contacts
  if (!searchQuery || searchQuery.trim() === "") {
    populateContactsTable(window._lastContactsList, false);
    return;
  }

  // Convert search query to lowercase for case-insensitive search
  const query = searchQuery.trim().toLowerCase();

  // Filter contacts based on name, email, phone, or contact type
  const filteredContacts = window._lastContactsList.filter((contact) => {
    const name = (contact.name || "").toLowerCase();
    const email = (contact.email || "").toLowerCase();
    const phone = (contact.phone || "").toLowerCase();
    const contactType = (contact.contact_type || "").toLowerCase();
    const caseLink = (contact.case_link || "").toLowerCase();

    return (
      name.includes(query) ||
      email.includes(query) ||
      phone.includes(query) ||
      contactType.includes(query) ||
      caseLink.includes(query)
    );
  });

  // Re-populate the table with filtered results
  populateContactsTable(filteredContacts, false);
}

/**
 * Populates the contacts table
 * @param {Array} contacts - Array of contact objects
 * @param {boolean} saveAsMainList - Whether to save this as the main contacts list (default: true)
 */
function populateContactsTable(contacts, saveAsMainList = true) {
  // Check if contacts table exists
  const tableBody = document.querySelector("#contacts-table tbody");
  if (!tableBody) {
    console.error("Contacts table tbody not found!");
    return;
  }

  // Ensure Edit Contact Modal HTML is present
  if (!document.getElementById("edit-contact-modal")) {
    const modalHtml = `
      <div class="modal" id="edit-contact-modal" style="display:none; position:fixed; z-index:9999; left:0; top:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); align-items:center; justify-content:center;">
        <div class="modal-content" style="background:#fff; padding:24px; border-radius:8px; min-width:320px; max-width:90vw; position:relative;">
          <span class="close-modal" style="position:absolute; right:12px; top:8px; font-size:24px; cursor:pointer;">&times;</span>
          <h3>Edit Contact</h3>
          <form id="edit-contact-form">
            <input type="hidden" name="id" id="edit-contact-id">
            <div style="margin-bottom:12px;">
              <label>Name:</label><br>
              <input type="text" name="name" id="edit-contact-name" style="width:100%">
            </div>
            <div style="margin-bottom:12px;">
              <label>Email:</label><br>
              <input type="email" name="email" id="edit-contact-email" style="width:100%">
            </div>
            <div style="margin-bottom:12px;">
              <label>Phone:</label><br>
              <input type="text" name="phone" id="edit-contact-phone" style="width:100%">
            </div>
            <button type="submit" class="btn btn-primary">Save</button>
          </form>
        </div>
      </div>
    `;
    document.body.insertAdjacentHTML("beforeend", modalHtml);

    // Add direct submit handler to the form
    const form = document.getElementById("edit-contact-form");
    if (form) {
      form.addEventListener("submit", function (e) {
        e.preventDefault();
        handleEditContactSubmit(e.target);
      });
    }
  }

  tableBody.innerHTML = "";

  if (!Array.isArray(contacts) || contacts.length === 0) {
    tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="empty-state">
            <i class="fas fa-address-book"></i>
            <p>No contacts found</p>
          </td>
        </tr>
      `;
    return;
  }

  contacts.forEach((contact, index) => {
    const row = document.createElement("tr");
    const {
      id = null,
      name = "N/A",
      contact_type = "Staff/External",
      email = "N/A",
      phone = "N/A",
      case_link = "N/A",
      last_contact = "N/A",
    } = contact;

    row.innerHTML = `
        <td>${name}</td>
        <td>${contact_type}</td>
        <td>${email}</td>
        <td>${phone}</td>
        <td>${case_link}</td>
        <td>${last_contact}</td>
        <td class="actions">
          <button class="btn-icon btn-edit-contact" data-id="${id}" title="Edit"><i class="fas fa-edit"></i></button>
          <button class="btn-icon btn-delete-contact" data-id="${id}" title="Delete"><i class="fas fa-trash"></i></button>
        </td>
      `;
    tableBody.appendChild(row);
  });

  // Edit button event handler
  document.querySelectorAll(".btn-edit-contact").forEach((btn, index) => {
    btn.addEventListener("click", (e) => {
      const row = e.target.closest("tr");
      const id = btn.getAttribute("data-id");

      // Find contact object from contacts array (if available)
      let contact = null;
      if (window._lastContactsList && Array.isArray(window._lastContactsList)) {
        contact = window._lastContactsList.find(
          (c) => String(c.id) === String(id)
        );
      }
      if (!contact) {
        // fallback: get from row
        contact = {
          id,
          name: row.children[0].innerText,
          contact_type: row.children[1].innerText,
          email: row.children[2].innerText,
          phone: row.children[3].innerText,
        };
      }
      showEditContactModal(contact);
    });
  });

  // Delete button event handler
  document.querySelectorAll(".btn-delete-contact").forEach((btn) => {
    btn.addEventListener("click", async (e) => {
      const row = e.target.closest("tr");
      const name = row.children[0].innerText;
      const id = btn.getAttribute("data-id");
      if (confirm(`Are you sure you want to delete "${name}"?`)) {
        try {
          const resp = await fetch(`/api/contacts/${id}`, { method: "DELETE" });
          if (resp.ok) {
            row.remove();
          } else {
            const err = await resp.json();
            alert("Failed to delete contact: " + (err.error || resp.status));
          }
        } catch (err) {
          alert("Failed to delete contact: " + err);
        }
      }
    });
  });

  // Save contacts list for modal use (only when not filtering)
  if (saveAsMainList) {
    window._lastContactsList = contacts;
  }
}

/**
 * Fetches all dashboard overview data
 */
function fetchDashboardData() {
  fetchDeadlines();
  fetchActiveCasesOnly();
  fetchAndRenderNotifications();
  loadAttorneyDocumentActivity();
}

/**
 * Fetches deadlines data
 */
function fetchDeadlines() {
  fetch("/api/attorney/deadlines")
    .then((response) => response.json())
    .then((deadlines) => renderDeadlines(deadlines))
    .catch((error) => {
      console.error("Error fetching deadlines:", error);
      showError("deadlines-list", "Failed to load deadlines");
    });
}

/**
 * Renders deadlines with color coding
 * @param {Array} deadlines - Array of deadline objects
 */
function renderDeadlines(deadlines) {
  const container = document.getElementById("deadlines-list");
  container.innerHTML = "";

  if (!deadlines || deadlines.length === 0) {
    container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-calendar-check"></i>
          <p>No upcoming deadlines</p>
        </div>
      `;
    return;
  }

  const ul = document.createElement("ul");
  ul.classList.add("deadline-items");

  // Sort by date
  deadlines.sort(
    (a, b) => new Date(a.deadline_date) - new Date(b.deadline_date)
  );

  deadlines.slice(0, 5).forEach((deadline) => {
    const li = document.createElement("li");

    // Determine deadline urgency
    const deadlineDate = new Date(deadline.deadline_date);
    const today = new Date();
    const daysUntilDeadline = Math.floor(
      (deadlineDate - today) / (1000 * 60 * 60 * 24)
    );

    let deadlineClass = "";
    if (daysUntilDeadline <= 3) {
      deadlineClass = "urgent";
    } else if (daysUntilDeadline <= 7) {
      deadlineClass = "upcoming";
    }

    li.className = `deadline-item ${deadlineClass}`;
    li.innerHTML = `
        <div class="deadline-header">
          <span class="deadline-title">${deadline.deadline_type}</span>
          <span class="deadline-date">${formatDate(
            deadline.deadline_date
          )}</span>
        </div>
        <div class="deadline-details">
          <span class="case-reference">Case #${deadline.case_id}</span>
          ${
            deadline.notes
              ? `<p class="deadline-notes">${deadline.notes}</p>`
              : ""
          }
        </div>
      `;
    ul.appendChild(li);
  });

  if (deadlines.length > 5) {
    const viewAll = document.createElement("a");
    viewAll.href = "/calendar";
    viewAll.className = "view-all-link";
    viewAll.textContent = "View all deadlines →";
    container.appendChild(viewAll);
  }

  container.appendChild(ul);
}

/**
 * Fetches only active cases for the overview
 */
function fetchActiveCasesOnly() {
  fetch("/api/get-all-cases-by-attorney")
    .then((res) => res.json())
    .then((cases) => {
      const openCases = cases.filter(
        (c) => c.case_status?.toLowerCase() === "open"
      );
      renderActiveCases(openCases);
    })
    .catch((err) => {
      console.error("Failed to load active cases", err);
      showError("active-cases", "Failed to load active cases");
    });
}

/**
 * Renders active cases in the overview
 * @param {Array} cases - Array of case objects
 */
function renderActiveCases(cases) {
  const container = document
    .getElementById("active-cases")
    .querySelector(".card-content");
  container.innerHTML = "";

  if (!cases || cases.length === 0) {
    container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-folder-open"></i>
          <p>No active cases</p>
        </div>
      `;
    return;
  }

  const ul = document.createElement("ul");
  ul.classList.add("active-cases-list");

  cases.slice(0, 5).forEach((caseItem) => {
    const li = document.createElement("li");
    li.className = "active-case-item";
    li.dataset.caseId = caseItem.case_id;
    li.innerHTML = `
        <div class="case-header">
          <span class="case-name">${
            caseItem.case_name || "Untitled Case"
          }</span>
          <span class="case-status ${caseItem.case_status.toLowerCase()}">${
      caseItem.case_status
    }</span>
        </div>
        <div class="case-details">
          <span class="case-type">${caseItem.case_type}</span>
          <span class="client-name">${caseItem.client_name}</span>
        </div>
      `;

    li.addEventListener("click", () => {
      window.location.href = `/case/${caseItem.case_id}/details`;
    });

    ul.appendChild(li);
  });

  if (cases.length > 5) {
    const viewAll = document.createElement("a");
    viewAll.href = "/cases";
    viewAll.className = "view-all-link";
    viewAll.textContent = "View all cases →";
    container.appendChild(viewAll);
  }

  container.appendChild(ul);
}

/**
 * Fetches and renders notifications
 */
function fetchAndRenderNotifications() {
  fetch("/api/notifications")
    .then((res) => res.json())
    .then((data) => {
      renderNotifications(data);
    })
    .catch((err) => {
      console.error("Error fetching notifications:", err);
      showError("notifications", "Failed to load notifications");
    });
}

/**
 * Renders notifications
 * @param {Array} notifications - Array of notification objects
 */
function renderNotifications(notifications) {
  const container = document
    .getElementById("notifications")
    .querySelector(".card-content");
  container.innerHTML = "";

  if (!notifications || notifications.length === 0) {
    container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-bell-slash"></i>
          <p>No new notifications</p>
        </div>
      `;
    return;
  }

  const ul = document.createElement("ul");
  ul.classList.add("notification-items");

  notifications.slice(0, 5).forEach((notification) => {
    const li = document.createElement("li");
    li.className = `notification-item ${notification.is_read ? "" : "unread"}`;
    li.dataset.id = notification.id;
    li.dataset.relatedId = notification.related_id;

    li.innerHTML = `
        <div class="notification-header">
          <span class="notification-type ${notification.type}">${
      notification.type
    }</span>
          <small class="notification-date">${formatDateTime(
            notification.created_at
          )}</small>
        </div>
        <p class="notification-message">${notification.message}</p>
      `;

    li.addEventListener("click", (e) => {
      if (notification.related_id) {
        window.location.href = `/case/${notification.related_id}/details`;
      }
      markNotificationAsRead(notification.id);
    });

    ul.appendChild(li);
  });

  if (notifications.length > 5) {
    const viewAll = document.createElement("a");
    viewAll.href = "/notifications";
    viewAll.className = "view-all-link";
    viewAll.textContent = "View all notifications →";
    container.appendChild(viewAll);
  }

  const markAllButton = document.createElement("button");
  markAllButton.textContent = "Mark All as Read";
  markAllButton.classList.add("mark-all-read");
  markAllButton.addEventListener("click", () => {
    markAllNotificationsAsRead();
  });

  container.appendChild(ul);
  container.appendChild(markAllButton);
}

/**
 * Marks a notification as read
 * @param {string} notificationId - ID of the notification to mark as read
 */
function markNotificationAsRead(notificationId) {
  fetch("/api/notifications/mark-read", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ notification_ids: [notificationId] }),
  }).then(() => {
    fetchAndRenderNotifications();
  });
}

/**
 * Marks all notifications as read
 */
function markAllNotificationsAsRead() {
  fetch("/api/notifications/mark-read", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ notification_ids: [] }),
  }).then(() => {
    fetchAndRenderNotifications();
  });
}

/**
 * Loads attorney document activity
 */
function loadAttorneyDocumentActivity() {
  fetch("/api/attorney/all-documents")
    .then((response) => {
      if (!response.ok) throw new Error("Failed to fetch documents");
      return response.json();
    })
    .then((documents) => {
      renderDocumentActivity(documents);
    })
    .catch((error) => {
      console.error("Error loading document activity:", error);
      showError("document-activity", "Failed to load documents");
    });
}

/**
 * Renders document activity
 * @param {Array} documents - Array of document objects
 */
function renderDocumentActivity(documents) {
  const container = document
    .getElementById("document-activity")
    .querySelector(".card-content");
  container.innerHTML = "";

  if (!documents || documents.length === 0) {
    container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-file-upload"></i>
          <p>No documents uploaded</p>
        </div>
      `;
    return;
  }

  // Sort by most recent first
  documents.sort((a, b) => new Date(b.uploaded_at) - new Date(a.uploaded_at));

  const ul = document.createElement("ul");
  ul.classList.add("document-list");

  documents.slice(0, 5).forEach((doc) => {
    const li = document.createElement("li");
    li.className = "document-item";
    li.innerHTML = `
        <div class="document-header">
          <span class="document-name">${truncate(doc.original_name, 30)}</span>
          <span class="document-date">${formatDate(doc.uploaded_at)}</span>
        </div>
        <div class="document-details">
          <span class="document-type">${doc.document_type}</span>
          <span class="document-case">Case #${doc.case_id}</span>
        </div>
      `;
    li.addEventListener("click", () => {
      const documentId = doc.id || doc.document_id;
      if (documentId) {
        window.location.href = `/document/${documentId}/details-page`;
      } else {
        console.warn("Missing document ID:", doc);
      }
    });
    ul.appendChild(li);
  });

  container.appendChild(ul);

  if (documents.length > 5) {
    const viewAll = document.createElement("a");
    viewAll.href = "/documents";
    viewAll.className = "view-all-link";
    viewAll.textContent = "View all documents →";
    container.appendChild(viewAll);
  }
}

/**
 * Sets up socket.io listeners
 * @param {Object} socket - Socket.io connection object
 */
function setupSocketListeners(socket) {
  socket.on("new_notification", (data) => {
    fetchAndRenderNotifications();
    showToast(data.message);
  });

  socket.on("case_update", (data) => {
    if (document.querySelector(".tab-content.active").id === "cases-tab") {
      fetchCases();
    }
    showToast(`Case ${data.case_id} updated: ${data.message}`);
  });

  socket.on("deadline_reminder", (data) => {
    if (document.querySelector(".tab-content.active").id === "overview-tab") {
      fetchDeadlines();
    }
    showToast(`Deadline reminder: ${data.message}`);
  });
}

/**
 * Shows a toast notification
 * @param {string} message - The message to display
 */
function showToast(message) {
  const toast = document.createElement("div");
  toast.className = "notification-toast";
  toast.innerHTML = `
      <i class="fas fa-info-circle"></i>
      <span>${message}</span>
    `;
  document.body.appendChild(toast);

  setTimeout(() => {
    toast.classList.add("fade-out");
    setTimeout(() => document.body.removeChild(toast), 300);
  }, 5000);
}

/**
 * Sets up event listeners for buttons
 */
function setupEventListeners() {
  // Add client button
  document.getElementById("add-client-btn")?.addEventListener("click", () => {
    window.location.href = "/add-client-form";
  });

  // Add case button
  document.getElementById("add-case-btn")?.addEventListener("click", () => {
    window.location.href = "/add_case";
  });

  // Add contact button
  document.getElementById("add-contact-btn")?.addEventListener("click", () => {
    window.location.href = "/add-contact-form";
  });

  // Contact search functionality
  const contactSearchInput = document.getElementById("contact-search");
  if (contactSearchInput) {
    contactSearchInput.addEventListener("input", (e) => {
      filterContacts(e.target.value);
    });

    contactSearchInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        filterContacts(e.target.value);
      }
    });
  }

  // Refresh dashboard button
  document
    .getElementById("refresh-dashboard")
    ?.addEventListener("click", () => {
      fetchDashboardData();
      showToast("Dashboard refreshed");
    });
}

/**
 * Formats a date string
 * @param {string} dateString - The date string to format
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
  if (!dateString) return "N/A";
  const options = { year: "numeric", month: "short", day: "numeric" };
  return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Formats a date/time string
 * @param {string} dateString - The date/time string to format
 * @returns {string} Formatted date/time
 */
function formatDateTime(dateString) {
  if (!dateString) return "N/A";
  const options = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };
  return new Date(dateString).toLocaleDateString(undefined, options);
}

/**
 * Truncates a string
 * @param {string} str - The string to truncate
 * @param {number} n - Maximum length
 * @returns {string} Truncated string
 */
function truncate(str, n) {
  return str.length > n ? str.substr(0, n - 1) + "..." : str;
}

/**
 * Shows an error message
 * @param {string} elementId - ID of the element to show error in
 * @param {string} message - Error message to display
 */
function showError(elementId, message) {
  const element =
    typeof elementId === "string"
      ? document.getElementById(elementId)
      : elementId;
  if (element) {
    element.innerHTML = `
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <p>${message}</p>
        </div>
      `;
  }
}

// Add Edit Contact Modal HTML if not present
if (!document.getElementById("edit-contact-modal")) {
  const modalHtml = `
    <div class="modal" id="edit-contact-modal" style="display:none; position:fixed; z-index:9999; left:0; top:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); align-items:center; justify-content:center;">
      <div class="modal-content" style="background:#fff; padding:24px; border-radius:8px; min-width:320px; max-width:90vw; position:relative;">
        <span class="close-modal" style="position:absolute; right:12px; top:8px; font-size:24px; cursor:pointer;">&times;</span>
        <h3>Edit Contact</h3>
        <form id="edit-contact-form">
          <input type="hidden" name="id" id="edit-contact-id">
          <div style="margin-bottom:12px;">
            <label>Name:</label><br>
            <input type="text" name="name" id="edit-contact-name" style="width:100%">
          </div>
          <div style="margin-bottom:12px;">
            <label>Email:</label><br>
            <input type="email" name="email" id="edit-contact-email" style="width:100%">
          </div>
          <div style="margin-bottom:12px;">
            <label>Phone:</label><br>
            <input type="text" name="phone" id="edit-contact-phone" style="width:100%">
          </div>
          <button type="submit" class="btn btn-primary">Save</button>
        </form>
      </div>
    </div>
  `;
  document.body.insertAdjacentHTML("beforeend", modalHtml);
}

function showEditContactModal(contact) {
  // contact: {id, name, email, phone, ...}
  document.getElementById("edit-contact-id").value = contact.id || "";
  document.getElementById("edit-contact-name").value = contact.name || "";
  document.getElementById("edit-contact-email").value = contact.email || "";
  document.getElementById("edit-contact-phone").value = contact.phone || "";
  document.getElementById("edit-contact-modal").style.display = "flex";
}

// Close modal handler
if (!window._editContactModalSetup) {
  document.body.addEventListener("click", function (e) {
    if (e.target.classList.contains("close-modal")) {
      document.getElementById("edit-contact-modal").style.display = "none";
    }
    // Click outside modal-content closes modal
    if (e.target.id === "edit-contact-modal") {
      document.getElementById("edit-contact-modal").style.display = "none";
    }
  });

  // Add submit handler for edit contact form
  document.body.addEventListener("submit", function (e) {
    // Check if the form being submitted is the edit contact form
    if (e.target && e.target.id === "edit-contact-form") {
      e.preventDefault();
      handleEditContactSubmit(e.target);
    } else if (
      e.target &&
      e.target.closest &&
      e.target.closest("#edit-contact-form")
    ) {
      // Fallback: check if the event target is inside the edit contact form
      e.preventDefault();
      handleEditContactSubmit(e.target.closest("#edit-contact-form"));
    }
  });

  window._editContactModalSetup = true;
}

// Handle edit contact form submission
async function handleEditContactSubmit(form) {
  const formData = new FormData(form);
  const contactId = formData.get("id");
  const contactData = {
    name: formData.get("name"),
    email: formData.get("email"),
    phone: formData.get("phone"),
  };

  try {
    const response = await fetch(`/api/contacts/${contactId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(contactData),
    });

    const result = await response.json();

    if (response.ok) {
      // Close modal
      document.getElementById("edit-contact-modal").style.display = "none";
      // Show success message
      alert("Contact updated successfully!");
      // Refresh contacts table
      await fetchContacts();
    } else {
      alert("Error updating contact: " + (result.error || "Unknown error"));
    }
  } catch (error) {
    console.error("Error updating contact:", error);
    alert("Error updating contact. Please try again.");
  }
}
