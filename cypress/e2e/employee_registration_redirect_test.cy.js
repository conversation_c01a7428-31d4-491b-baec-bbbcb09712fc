describe("Employee Registration Redirect Test", () => {
  it("should show modal with redirect options after successful employee registration", () => {
    // Use a unique timestamp for test data
    const timestamp = Date.now();
    const testData = {
      name: `Test Employee ${timestamp}`,
      email: `employee_${timestamp}@test.com`,
      username: `test_employee_${timestamp}`,
      password: "password123",
      role: "Paralegal",
      hourly_rate: "25",
      law_firm_name: "Test Law Firm",
    };

    // Visit the employee registration page
    cy.visit("/register/employee");

    // Verify the form is visible
    cy.get("#employeeRegistrationForm").should("be.visible");

    // Fill out the registration form
    cy.get("#name").type(testData.name);
    cy.get("#email").type(testData.email);
    cy.get("#username").type(testData.username);
    cy.get("#password").type(testData.password);
    cy.get("#role").select(testData.role);
    cy.get("#hourly_rate").type(testData.hourly_rate);
    cy.get("#law_firm_id").type(testData.law_firm_name);

    // Submit the form
    cy.get('button[type="submit"]').click();

    // Wait for the success message
    cy.get("#registration_message", { timeout: 10000 })
      .should("be.visible")
      .and("contain", "Employee registered successfully");

    // Check that the confirmation modal appears
    cy.get(".confirmation-modal", { timeout: 5000 }).should("be.visible");

    // Verify modal content
    cy.get(".confirmation-modal")
      .should("contain", "Registration Successful!")
      .and("contain", "What would you like to do next?")
      .and("contain", "Automatically redirecting to dashboard");

    // Verify countdown timer is present
    cy.get("#countdown-timer").should("be.visible");

    // Verify both buttons are present
    cy.get("#goToDashboard")
      .should("be.visible")
      .and("contain", "Go to Dashboard");

    cy.get("#goToLogin").should("be.visible").and("contain", "Go to Login");

    // Test clicking the "Go to Dashboard" button
    cy.get("#goToDashboard").click();

    // Verify redirect to employee dashboard
    cy.url().should("include", "/employee/dashboard");
    cy.url().should("include", "new_employee=");

    // Verify the employee dashboard loads
    cy.contains("Welcome back,").should("be.visible");
  });

  it("should automatically redirect after countdown expires", () => {
    // Use a unique timestamp for test data
    const timestamp = Date.now();
    const testData = {
      name: `Test Employee Auto ${timestamp}`,
      email: `employee_auto_${timestamp}@test.com`,
      username: `test_employee_auto_${timestamp}`,
      password: "password123",
      role: "Paralegal",
      hourly_rate: "25",
      law_firm_name: "Test Law Firm",
    };

    // Visit the employee registration page
    cy.visit("/register/employee");

    // Fill out and submit the form
    cy.get("#name").type(testData.name);
    cy.get("#email").type(testData.email);
    cy.get("#username").type(testData.username);
    cy.get("#password").type(testData.password);
    cy.get("#role").select(testData.role);
    cy.get("#hourly_rate").type(testData.hourly_rate);
    cy.get("#law_firm_id").type(testData.law_firm_name);

    cy.get('button[type="submit"]').click();

    // Wait for the modal to appear
    cy.get(".confirmation-modal", { timeout: 5000 }).should("be.visible");

    // Wait for the countdown to reach a lower number (but not wait full 10 seconds)
    cy.get("#countdown-timer").should("contain", "10");

    // Wait a bit to see countdown decreasing
    cy.wait(2000);
    cy.get("#countdown-timer").should("not.contain", "10");

    // For testing purposes, we'll click the dashboard button instead of waiting 10 seconds
    // In a real scenario, you could wait the full 10 seconds or mock the timer
    cy.get("#goToDashboard").click();

    // Verify redirect
    cy.url().should("include", "/employee/dashboard");
  });

  it("should redirect to login page when clicking Go to Login button", () => {
    // Use a unique timestamp for test data
    const timestamp = Date.now();
    const testData = {
      name: `Test Employee Login ${timestamp}`,
      email: `employee_login_${timestamp}@test.com`,
      username: `test_employee_login_${timestamp}`,
      password: "password123",
      role: "Paralegal",
      hourly_rate: "25",
      law_firm_name: "Test Law Firm",
    };

    // Visit the employee registration page
    cy.visit("/register/employee");

    // Fill out and submit the form
    cy.get("#name").type(testData.name);
    cy.get("#email").type(testData.email);
    cy.get("#username").type(testData.username);
    cy.get("#password").type(testData.password);
    cy.get("#role").select(testData.role);
    cy.get("#hourly_rate").type(testData.hourly_rate);
    cy.get("#law_firm_id").type(testData.law_firm_name);

    cy.get('button[type="submit"]').click();

    // Wait for the modal to appear
    cy.get(".confirmation-modal", { timeout: 5000 }).should("be.visible");

    // Click the "Go to Login" button
    cy.get("#goToLogin").click();

    // Verify redirect to login page with username pre-filled
    cy.url().should("include", "/login");
    cy.url().should(
      "include",
      `username=${encodeURIComponent(testData.username)}`
    );
  });

  it("should close modal when clicking outside", () => {
    // Use a unique timestamp for test data
    const timestamp = Date.now();
    const testData = {
      name: `Test Employee Close ${timestamp}`,
      email: `employee_close_${timestamp}@test.com`,
      username: `test_employee_close_${timestamp}`,
      password: "password123",
      role: "Paralegal",
      hourly_rate: "25",
      law_firm_name: "Test Law Firm",
    };

    // Visit the employee registration page
    cy.visit("/register/employee");

    // Fill out and submit the form
    cy.get("#name").type(testData.name);
    cy.get("#email").type(testData.email);
    cy.get("#username").type(testData.username);
    cy.get("#password").type(testData.password);
    cy.get("#role").select(testData.role);
    cy.get("#hourly_rate").type(testData.hourly_rate);
    cy.get("#law_firm_id").type(testData.law_firm_name);

    cy.get('button[type="submit"]').click();

    // Wait for the modal to appear
    cy.get(".confirmation-modal", { timeout: 5000 }).should("be.visible");

    // Click outside the modal (on the modal backdrop)
    cy.get(".confirmation-modal").click({ force: true });

    // Verify modal is closed
    cy.get(".confirmation-modal").should("not.exist");
  });
});
