# 📁 case_management/tests/conftest.py

import pytest
import os
from case_management.case_api import create_app
from case_management.extensions import db
from user_management.config import TestingConfig, Config  # Make sure this import is here

@pytest.fixture
def app():
    config_class = Config if os.getenv("CI") == "true" else TestingConfig
    app = create_app(config_class)
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()
        db.engine.dispose()  # Properly dispose of engine connections

@pytest.fixture
def client(app):
    return app.test_client()
