# 📁 tests/test_external_contact_repository.py

import pytest
from datetime import datetime
from case_management.extensions import db
from case_management.models import ExternalContact, Case, Client, Attorney, LawFirm
from repositories.external_contact import ExternalContactRepository


class TestExternalContactRepository:
    """Test suite for ExternalContactRepository class."""

    @pytest.fixture(autouse=True)
    def setup_test_data(self, app):
        """Set up test data for each test."""
        with app.app_context():
            # Store the case_id as a string to avoid session issues
            self.case_id = "CASE001"
            self.law_firm_id = None
            self.attorney_id = "ATT001"
            self.client_id = "CLI001"

    def create_test_case(self):
        """Helper method to create test case within a test context."""
        # Create a test law firm
        law_firm = LawFirm(
            name="Test Law Firm",
            address="123 Test St",
            contact_email="<EMAIL>",
            phone_number="555-0123"
        )
        db.session.add(law_firm)
        db.session.commit()

        # Create a test attorney
        attorney = Attorney(
            attorney_id=self.attorney_id,
            name="Test Attorney",
            username="testattorney",
            password_hash="hashed_password",
            email="<EMAIL>",
            role="Partner",
            hourly_rate=350.00,
            law_firm_id=law_firm.id
        )
        db.session.add(attorney)
        db.session.commit()

        # Create a test client
        client_obj = Client(
            client_id=self.client_id,
            name="Test Client",
            username="testclient",
            password_hash="hashed_password",
            email="<EMAIL>",
            law_firm_id=law_firm.id
        )
        db.session.add(client_obj)
        db.session.commit()

        # Create a test case
        case = Case(
            case_id=self.case_id,
            client_id=client_obj.client_id,
            client_name=client_obj.name,
            case_name="Test Case",
            case_type="Civil",
            case_status="Active",
            attorney_id=attorney.attorney_id,
            law_firm_id=law_firm.id
        )
        db.session.add(case)
        db.session.commit()
        return case

    def test_create_contact_success_minimal_data(self, app):
        """Test creating a contact with minimal required data."""
        with app.app_context():
            # Create contact with only required field
            contact = ExternalContactRepository.create_contact(name="John Doe")
            
            assert contact is not None
            assert contact.name == "John Doe"
            assert contact.contact_type == "External"  # Default value
            assert contact.email is None
            assert contact.phone is None
            assert contact.role is None
            assert contact.case_link is None
            assert contact.last_contact is None
            assert contact.last_login is None
            assert contact.date_added is not None

    def test_create_contact_success_full_data(self, app):
        """Test creating a contact with all fields populated."""
        with app.app_context():
            # Create test case first
            case = self.create_test_case()
            test_datetime = datetime(2025, 1, 15, 10, 30, 0)
            
            contact = ExternalContactRepository.create_contact(
                name="Jane Smith",
                contact_type="Expert Witness",
                email="<EMAIL>",
                phone="555-1234",
                role="Medical Expert",
                case_link=case.case_id,
                last_contact=test_datetime
            )
            
            assert contact is not None
            assert contact.name == "Jane Smith"
            assert contact.contact_type == "Expert Witness"
            assert contact.email == "<EMAIL>"
            assert contact.phone == "555-1234"
            assert contact.role == "Medical Expert"
            assert contact.case_link == case.case_id
            assert contact.last_contact == test_datetime
            assert contact.last_login is None
            assert contact.date_added is not None

    def test_create_contact_database_persistence(self, app):
        """Test that created contact is properly saved to database."""
        with app.app_context():
            # Create contact
            contact = ExternalContactRepository.create_contact(
                name="Database Test Contact",
                contact_type="Witness",
                email="<EMAIL>"
            )
            
            # Verify it was saved
            assert contact is not None
            assert contact.id is not None
            
            # Query database directly to verify persistence
            saved_contact = ExternalContact.query.filter_by(id=contact.id).first()
            assert saved_contact is not None
            assert saved_contact.name == "Database Test Contact"
            assert saved_contact.contact_type == "Witness"
            assert saved_contact.email == "<EMAIL>"

    def test_create_contact_with_case_link(self, app):
        """Test creating a contact linked to a specific case."""
        with app.app_context():
            # Create test case first
            case = self.create_test_case()
            
            contact = ExternalContactRepository.create_contact(
                name="Case Linked Contact",
                contact_type="Opposing Counsel",
                email="<EMAIL>",
                case_link=case.case_id
            )
            
            assert contact is not None
            assert contact.case_link == case.case_id
            
            # Verify the case relationship works
            linked_case = Case.query.filter_by(case_id=contact.case_link).first()
            assert linked_case is not None
            assert linked_case.case_id == case.case_id

    def test_create_contact_empty_name_fails(self, app):
        """Test that creating a contact with empty name fails."""
        with app.app_context():
            # Test with None name
            contact = ExternalContactRepository.create_contact(name=None)
            assert contact is None
            
            # Test with empty string name
            contact = ExternalContactRepository.create_contact(name="")
            assert contact is None

    def test_create_contact_default_contact_type(self, app):
        """Test that contact_type defaults to 'External' when not provided."""
        with app.app_context():
            contact = ExternalContactRepository.create_contact(
                name="Default Type Test",
                email="<EMAIL>"
            )
            
            assert contact is not None
            assert contact.contact_type == "External"

    def test_create_contact_override_default_type(self, app):
        """Test that providing contact_type overrides the default."""
        with app.app_context():
            contact = ExternalContactRepository.create_contact(
                name="Custom Type Test",
                contact_type="Judge",
                email="<EMAIL>"
            )
            
            assert contact is not None
            assert contact.contact_type == "Judge"

    def test_create_contact_with_datetime_field(self, app):
        """Test creating a contact with last_contact datetime field."""
        with app.app_context():
            test_datetime = datetime(2025, 7, 21, 14, 30, 0)
            
            contact = ExternalContactRepository.create_contact(
                name="DateTime Test Contact",
                last_contact=test_datetime
            )
            
            assert contact is not None
            assert contact.last_contact == test_datetime

    def test_create_contact_multiple_contacts(self, app):
        """Test creating multiple contacts to ensure no conflicts."""
        with app.app_context():
            contacts = []
            
            for i in range(3):
                contact = ExternalContactRepository.create_contact(
                    name=f"Contact {i+1}",
                    contact_type="Test Type",
                    email=f"contact{i+1}@test.com"
                )
                contacts.append(contact)
            
            # Verify all contacts were created
            assert len(contacts) == 3
            for i, contact in enumerate(contacts):
                assert contact is not None
                assert contact.name == f"Contact {i+1}"
                assert contact.email == f"contact{i+1}@test.com"
            
            # Verify they all have unique IDs
            contact_ids = [c.id for c in contacts]
            assert len(set(contact_ids)) == 3  # All IDs should be unique

    def test_create_contact_error_handling(self, app):
        """Test error handling in create_contact method."""
        with app.app_context():
            # Test with invalid case_link (non-existent case)
            # Note: SQLite doesn't enforce foreign key constraints by default,
            # so we test other error conditions instead
            contact = ExternalContactRepository.create_contact(
                name="Error Test Contact",
                case_link="INVALID_CASE_ID"
            )
            
            # Since FK constraints may not be enforced in SQLite test environment,
            # the contact might still be created, so we test other error conditions
            # Just ensure the method returns something (either success or failure)
            # The important thing is that the method doesn't crash
            
            # Test with valid contact (no case_link)
            valid_contact = ExternalContactRepository.create_contact(
                name="Valid Contact"
            )
            assert valid_contact is not None
            assert valid_contact.case_link is None

    def test_create_contact_email_validation(self, app):
        """Test that various email formats are accepted."""
        with app.app_context():
            test_emails = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                ""  # Empty string should be allowed
            ]
            
            for i, email in enumerate(test_emails):
                contact = ExternalContactRepository.create_contact(
                    name=f"Email Test {i+1}",
                    email=email if email else None
                )
                assert contact is not None
                assert contact.email == (email if email else None)

    def test_create_contact_phone_validation(self, app):
        """Test that various phone formats are accepted."""
        with app.app_context():
            test_phones = [
                "555-1234",
                "(*************",
                "******-123-4567",
                "5551234567",
                ""  # Empty string should be allowed
            ]
            
            for i, phone in enumerate(test_phones):
                contact = ExternalContactRepository.create_contact(
                    name=f"Phone Test {i+1}",
                    phone=phone if phone else None
                )
                assert contact is not None
                assert contact.phone == (phone if phone else None)

    def test_create_contact_rollback_on_error(self, app):
        """Test that database rollback works properly on errors."""
        with app.app_context():
            # Get initial count
            initial_count = ExternalContact.query.count()
            
            # Simulate an error by trying to create contact with very long name
            # that exceeds database field limit (assuming 255 char limit)
            very_long_name = "A" * 1000  # Much longer than typical field limit
            
            contact = ExternalContactRepository.create_contact(name=very_long_name)
            
            # Check that no contact was created due to error
            final_count = ExternalContact.query.count()
            assert final_count == initial_count
            assert contact is None
