import os
from pathlib import Path
from dotenv import load_dotenv
import urllib.parse

# Load the .env file from the database directory
dotenv_path = Path(__file__).resolve().parent.parent / "database" / ".env"
load_dotenv(dotenv_path)

# Load an additional .env based on environment (optional fallback)
if os.environ.get('FLASK_ENV') == 'docker':
    load_dotenv('.env')
else:
    load_dotenv('.env.local')

print("DB_USERNAME:", os.getenv("DB_USERNAME"))
print("DB_PASSWORD:", os.getenv("DB_PASSWORD"))

class Config:
    # General Flask settings
    TESTING = False
    SECRET_KEY = os.getenv("FLASK_SECRET_KEY", 'fallback_key_for_dev')
    SECURITY_PASSWORD_SALT = os.getenv('SECURITY_PASSWORD_SALT', 'fallback_salt_for_dev')

    # Stripe API keys
    STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY')
    STRIPE_PUBLIC_KEY = os.getenv('STRIPE_PUBLIC_KEY')

    # Mail settings
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.example.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'True') == 'True'
    MAIL_USE_SSL = os.getenv('MAIL_USE_SSL', 'False') == 'True'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', 'your-email-password')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', MAIL_USERNAME)

    # File upload directory
    BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    UPLOAD_FOLDER = os.path.join(BASE_DIR, "case_management", "uploads")

    # Construct MySQL database URI directly from environment
    DB_USERNAME = os.getenv("DB_USERNAME", "default_username")
    DB_PASSWORD = urllib.parse.quote(os.getenv("DB_PASSWORD", "default_password")).replace('%', '%%')
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_NAME = os.getenv("DB_NAME", "default_db")

    SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{DB_USERNAME}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # SQLAlchemy connection pool settings for better connection management
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': 3600,
        'max_overflow': 20,
        'pool_pre_ping': True,
        'pool_reset_on_return': 'commit'
    }
    
    # ... other config ...
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://redis:6379/0')
    CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://redis:6379/0')

class TestingConfig(Config):
    """
    Configuration used for running tests — now uses SQLite for local testing, MySQL in CI.
    """
    TESTING = True
    WTF_CSRF_ENABLED = False  # Disable CSRF for test forms
    # Mail overrides for test isolation
    MAIL_USERNAME = os.getenv('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', 'testpassword')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', MAIL_USERNAME)
    # Use SQLite for local tests, MySQL in CI
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{Config.DB_USERNAME}:{Config.DB_PASSWORD}@{Config.DB_HOST}/{Config.DB_NAME}"
        if os.getenv("CI") == "true" else "sqlite:///test.db"
    )
    
    # Override connection pool settings for testing
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 5,
        'pool_timeout': 30,
        'pool_recycle': 1800,
        'max_overflow': 10,
        'pool_pre_ping': True,
        'pool_reset_on_return': 'rollback'
    } if os.getenv("CI") == "true" else {}
