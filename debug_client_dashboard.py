#!/usr/bin/env python3
"""
Debug script to test client dashboard functionality
Run this script to test the client creation and retrieval process
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_client_dashboard_debug():
    """Test client dashboard functionality"""
    
    print("🔍 Client Dashboard Debug Test")
    print("=" * 50)
    
    try:
        # Import required modules
        from case_management.case_api import create_app
        from repositories.client import ClientRepository
        from repositories.attorney import AttorneyRepository
        from models.client import Client
        from models.attorney import Attorney
        
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            print("✅ Flask app context created successfully")
            
            # Test 1: Check if attorneys exist
            print("\n📋 Test 1: Checking attorneys in database...")
            attorneys = AttorneyRepository.get_all_attorneys()
            print(f"Found {len(attorneys)} attorneys in database")
            
            if attorneys:
                for attorney in attorneys[:3]:  # Show first 3
                    print(f"  - {attorney.name} (ID: {attorney.attorney_id})")
            else:
                print("❌ No attorneys found in database!")
                return False
            
            # Test 2: Check clients for first attorney
            print("\n📋 Test 2: Checking clients for first attorney...")
            first_attorney = attorneys[0]
            print(f"Testing with attorney: {first_attorney.name} (ID: {first_attorney.attorney_id})")
            
            # Get primary clients
            primary_clients = ClientRepository.get_clients_by_primary_attorney(first_attorney.attorney_id)
            print(f"Primary clients: {len(primary_clients)}")
            
            # Get collaborating clients
            collaborating_clients = ClientRepository.get_clients_by_collaborating_attorney(first_attorney.attorney_id)
            print(f"Collaborating clients: {len(collaborating_clients)}")
            
            # Show client details
            all_clients = list(set(primary_clients + collaborating_clients))
            print(f"Total unique clients: {len(all_clients)}")
            
            if all_clients:
                print("\n📋 Client Details:")
                for client in all_clients[:5]:  # Show first 5
                    print(f"  - {client.name} (ID: {client.client_id})")
                    print(f"    Email: {client.email}")
                    print(f"    Primary Attorney ID: {client.primary_attorney_id}")
                    print(f"    Law Firm ID: {client.law_firm_id}")
                    print()
            else:
                print("❌ No clients found for this attorney!")
                
                # Check if there are any clients in the database at all
                print("\n🔍 Checking all clients in database...")
                from models.client import Client
                all_db_clients = Client.query.all()
                print(f"Total clients in database: {len(all_db_clients)}")
                
                if all_db_clients:
                    print("Sample clients:")
                    for client in all_db_clients[:3]:
                        print(f"  - {client.name} (Primary Attorney: {client.primary_attorney_id})")
            
            # Test 3: Test route registration
            print("\n📋 Test 3: Checking route registration...")
            routes = []
            for rule in app.url_map.iter_rules():
                if 'attorney' in rule.rule.lower():
                    routes.append(f"{rule.rule} [{', '.join(rule.methods)}]")
            
            print(f"Found {len(routes)} attorney-related routes:")
            for route in routes:
                print(f"  - {route}")
            
            # Check specifically for our route
            client_routes = [r for r in routes if '/attorney/clients' in r]
            if client_routes:
                print("✅ /attorney/clients route is registered")
            else:
                print("❌ /attorney/clients route NOT found!")
            
            return True
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_simulation():
    """Simulate a session to test the route"""
    print("\n🔍 Session Simulation Test")
    print("=" * 30)
    
    try:
        from case_management.case_api import create_app
        from repositories.attorney import AttorneyRepository
        
        app = create_app()
        
        with app.test_client() as client:
            with app.app_context():
                # Get first attorney
                attorneys = AttorneyRepository.get_all_attorneys()
                if not attorneys:
                    print("❌ No attorneys found for session test")
                    return False
                
                first_attorney = attorneys[0]
                print(f"Testing with attorney: {first_attorney.name}")
                
                # Simulate login session
                with client.session_transaction() as sess:
                    sess['user_id'] = first_attorney.user_id
                    sess['attorney_id'] = first_attorney.attorney_id
                    sess['role'] = 'attorney'
                    sess['law_firm_id'] = first_attorney.law_firm_id
                
                # Test the route
                print("📡 Testing /attorney/clients route...")
                response = client.get('/attorney/clients')
                
                print(f"Response status: {response.status_code}")
                print(f"Response data: {response.get_data(as_text=True)[:200]}...")
                
                if response.status_code == 200:
                    print("✅ Route works correctly!")
                    import json
                    data = json.loads(response.get_data(as_text=True))
                    print(f"Returned {len(data)} clients")
                else:
                    print(f"❌ Route failed with status {response.status_code}")
                
                return response.status_code == 200
                
    except Exception as e:
        print(f"❌ Session test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Client Dashboard Debug Tests")
    print("=" * 60)
    
    success1 = test_client_dashboard_debug()
    success2 = test_session_simulation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"Database Test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Session Test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! The issue might be in the frontend or session management.")
        print("\n💡 Next steps:")
        print("1. Check browser console for JavaScript errors")
        print("2. Verify attorney is properly logged in")
        print("3. Check server logs when accessing the dashboard")
    else:
        print("\n🔧 Issues found! Check the error messages above.")
