<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attorney Registration - Case Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .registration-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 4rem auto 2rem auto; /* Increased top margin */
            max-width: 800px;
        }
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.2rem 2rem 1.2rem 2rem; /* Reduced top/bottom padding */
            text-align: center;
        }
        .form-section {
            padding: 2rem;
        }
        .plan-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.1);
        }
        .plan-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .plan-price {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .plan-card.selected .plan-price {
            color: white;
        }
        .plan-features {
            list-style: none;
            padding: 0;
        }
        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .plan-card.selected .plan-features li {
            border-bottom-color: rgba(255,255,255,0.3);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .loading {
            display: none;
        }
        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .success-message {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            margin-top: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        /* Added styles for password toggle */
        .password-input-group {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 20px;
            top: 70%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #667eea;
            z-index: 5;
        }
        /* Center the professional details button */
        #step2-content .d-flex.justify-content-between {
            justify-content: space-between !important;
            gap: 1rem;
        }
        /* Add spacing between buttons in step 2 */
        #step2-content .btn {
            margin: 0 0.5rem;
        }
        #step3-content .d-flex.justify-content-between {
            justify-content: space-between !important;
            gap: 1rem;
        }
        /* Add spacing between buttons in step 2 */
        #step3-content .btn {
            margin: 0 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="registration-container">
            <!-- Header Section -->
            <div class="header-section">
                <h1><i class="fas fa-gavel"></i> Attorney Registration</h1>
                <p class="mb-0">Join our legal case management platform</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
            </div>

            <!-- Registration Form -->
            <div class="form-section">
                <form id="attorneyRegistrationForm">
                    <!-- Step 1: Personal Information -->
                    <div id="step1-content">
                        <h3 class="mb-4">Personal Information</h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" required onblur="checkUsernameAvailability()">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required onblur="checkEmailAvailability()">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone_number" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3 password-input-group">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <i class="password-toggle fas fa-eye" onclick="togglePassword('password')"></i>
                            </div>
                            <div class="col-md-6 mb-3 password-input-group">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <i class="password-toggle fas fa-eye" onclick="togglePassword('confirm_password')"></i>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Professional Details</button>
                        </div>
                    </div>

                    <!-- Step 2: Professional Information -->
                    <div id="step2-content" style="display: none;">
                        <h3 class="mb-4">Professional Information</h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="specialization" class="form-label">Specialization *</label>
                                <select class="form-control" id="specialization" name="specialization" required>
                                    <option value="">Select Specialization</option>
                                    <option value="Criminal Law">Criminal Law</option>
                                    <option value="Civil Law">Civil Law</option>
                                    <option value="Corporate Law">Corporate Law</option>
                                    <option value="Family Law">Family Law</option>
                                    <option value="Real Estate Law">Real Estate Law</option>
                                    <option value="Intellectual Property">Intellectual Property</option>
                                    <option value="Tax Law">Tax Law</option>
                                    <option value="Employment Law">Employment Law</option>
                                    <option value="Environmental Law">Environmental Law</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role *</label>
                                <select class="form-control" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="Partner">Partner</option>
                                    <option value="Founder">Founder</option>
                                    <option value="Associate">Associate</option>
                                    <option value="Senior Partner">Senior Partner</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="hourly_rate" class="form-label">Hourly Rate (USD) *</label>
                                <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" min="50" max="1000" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Professional Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4" placeholder="Tell us about your experience and expertise..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="law_firm_id" class="form-label">Law Firm ID (Optional)</label>
                            <input type="text" class="form-control" id="law_firm_id" name="law_firm_id" placeholder="Enter your law firm ID if applicable">
                        </div>
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-secondary" onclick="prevStep()">Previous</button>
                            <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Choose Plan</button>
                        </div>
                    </div>

                    <!-- Step 3: Subscription Plan -->
                    <div id="step3-content" style="display: none;">
                        <h3 class="mb-4">Choose Your Subscription Plan</h3>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="plan-card" data-plan="monthly">
                                    <div class="text-center">
                                        <h4>Monthly</h4>
                                        <div class="plan-price">$9.99</div>
                                        <small>per month</small>
                                    </div>
                                    <ul class="plan-features">
                                        <li><i class="fas fa-check"></i> Full case management</li>
                                        <li><i class="fas fa-check"></i> Client portal</li>
                                        <li><i class="fas fa-check"></i> Document storage</li>
                                        <li><i class="fas fa-check"></i> Email support</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="plan-card" data-plan="6months">
                                    <div class="text-center">
                                        <h4>6 Months</h4>
                                        <div class="plan-price">$14.99</div>
                                        <small>per 6 months</small>
                                        <div class="badge bg-success">Save 25%</div>
                                    </div>
                                    <ul class="plan-features">
                                        <li><i class="fas fa-check"></i> Everything in Monthly</li>
                                        <li><i class="fas fa-check"></i> Priority support</li>
                                        <li><i class="fas fa-check"></i> Advanced analytics</li>
                                        <li><i class="fas fa-check"></i> Custom branding</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="plan-card" data-plan="yearly">
                                    <div class="text-center">
                                        <h4>Yearly</h4>
                                        <div class="plan-price">$17.99</div>
                                        <small>per year</small>
                                        <div class="badge bg-success">Save 85%</div>
                                    </div>
                                    <ul class="plan-features">
                                        <li><i class="fas fa-check"></i> Everything in 6 Months</li>
                                        <li><i class="fas fa-check"></i> 24/7 phone support</li>
                                        <li><i class="fas fa-check"></i> API access</li>
                                        <li><i class="fas fa-check"></i> White-label solution</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-secondary" onclick="prevStep()">Previous</button>
                            <button type="submit" class="btn btn-primary" id="registerBtn">
                                <span class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Processing...
                                </span>
                                <span class="normal">Complete Registration & Payment</span>
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Messages -->
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <div id="successMessage" class="success-message" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
// Real-time username/email check
window.checkUsernameAvailability = async function() {
    const username = document.getElementById('username').value.trim();
    if (!username) return;
    try {
        const res = await fetch(`/api/check-username?username=${encodeURIComponent(username)}`);
        const data = await res.json();
        if (!data.available) {
            showError('Username already exists. Please choose another.');
        } else {
            // Hide error if previously shown
            document.getElementById('errorMessage').style.display = 'none';
        }
    } catch (e) {
        console.error('[checkUsernameAvailability] AJAX error:', e);
        showError('Could not validate username. Try again.');
    }
}
window.checkEmailAvailability = async function() {
    const email = document.getElementById('email').value.trim();
    if (!email) return;
    try {
        const res = await fetch(`/api/check-email?email=${encodeURIComponent(email)}`);
        const data = await res.json();
        if (!data.available) {
            showError('Email already exists. Please use another.');
        } else {
            // Hide error if previously shown
            document.getElementById('errorMessage').style.display = 'none';
        }
    } catch (e) {
        console.error('[checkEmailAvailability] AJAX error:', e);
        showError('Could not validate email. Try again.');
    }
}

        let currentStep = 1;
        let selectedPlan = null;

        // Step navigation
        async function nextStep() {
            if (currentStep === 1) {
                // Clear any previous messages
                clearMessages();
                
                // Show loading state
                const nextBtn = document.querySelector('[onclick="nextStep()"]');
                if (nextBtn) {
                    nextBtn.disabled = true;
                    nextBtn.innerHTML = 'Validating...';
                }
                
                // Perform validation
                const isValid = await validateStep1();
                
                // Reset button state
                if (nextBtn) {
                    nextBtn.disabled = false;
                    nextBtn.innerHTML = 'Next: Professional Details';
                }
                
                if (!isValid) {
                    return; // Block progression
                }
                document.getElementById('step1-content').style.display = 'none';
                document.getElementById('step2-content').style.display = 'block';
                document.getElementById('step1').classList.add('completed');
                document.getElementById('step2').classList.add('active');
                currentStep = 2;
            } else if (currentStep === 2) {
                if (!validateStep2()) return;
                // Clear messages when successfully moving to next step
                clearMessages();
                document.getElementById('step2-content').style.display = 'none';
                document.getElementById('step3-content').style.display = 'block';
                document.getElementById('step2').classList.add('completed');
                document.getElementById('step3').classList.add('active');
                currentStep = 3;
            }
        }

        // Password toggle functionality
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.parentElement.querySelector('.password-toggle');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function prevStep() {
            // Clear any existing error messages when navigating between steps
            clearMessages();
            
            if (currentStep === 2) {
                document.getElementById('step2-content').style.display = 'none';
                document.getElementById('step1-content').style.display = 'block';
                document.getElementById('step2').classList.remove('active');
                document.getElementById('step1').classList.remove('completed');
                document.getElementById('step1').classList.add('active');
                currentStep = 1;
            } else if (currentStep === 3) {
                document.getElementById('step3-content').style.display = 'none';
                document.getElementById('step2-content').style.display = 'block';
                document.getElementById('step3').classList.remove('active');
                document.getElementById('step2').classList.remove('completed');
                document.getElementById('step2').classList.add('active');
                currentStep = 2;
            }
        }

        // Validation functions
        async function validateStep1() {
            const required = ['name', 'username', 'email', 'phone_number', 'address', 'password', 'confirm_password'];
            for (let field of required) {
                const value = document.getElementById(field).value.trim();
                if (!value) {
                    showError(`Please fill in ${field.replace('_', ' ')}`);
                    return false;
                }
            }

            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();

            // Check username availability
            let usernameAvailable = true;
            try {
                const res = await fetch(`/api/check-username?username=${encodeURIComponent(username)}`);
                const data = await res.json();
                usernameAvailable = data.available;
            } catch (e) {
                console.error('[validateStep1] Username check failed:', e);
                usernameAvailable = false;
            }
            if (!usernameAvailable) {
                showError('Username already exists. Please choose another.');
                return false;
            }

            // Check email availability
            let emailAvailable = true;
            try {
                const res = await fetch(`/api/check-email?email=${encodeURIComponent(email)}`);
                const data = await res.json();
                emailAvailable = data.available;
            } catch (e) {
                console.error('[validateStep1] Email check failed:', e);
                emailAvailable = false;
            }
            if (!emailAvailable) {
                showError('Email already exists. Please use another.');
                return false;
            }

            if (!isValidEmail(email)) {
                showError('Please enter a valid email address');
                return false;
            }

            const password = document.getElementById('password').value.trim();
            const confirmPassword = document.getElementById('confirm_password').value.trim();
            if (password !== confirmPassword) {
                showError('Passwords do not match');
                return false;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long');
                return false;
            }

            return true;
        }

        function validateStep2() {
            const specialization = document.getElementById('specialization').value;
            const hourlyRate = document.getElementById('hourly_rate').value;

            if (!specialization) {
                showError('Please select a specialization');
                return false;
            }

            if (!hourlyRate || hourlyRate < 50 || hourlyRate > 1000) {
                showError('Please enter a valid hourly rate between $50 and $1000');
                return false;
            }

            return true;
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Plan selection
        document.querySelectorAll('.plan-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.plan-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedPlan = this.dataset.plan;
            });
        });

        // Form submission
        document.getElementById('attorneyRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!selectedPlan) {
                showError('Please select a subscription plan');
                return;
            }

            // Validate both steps
            const step1Valid = await validateStep1();
            const step2Valid = validateStep2();
            
            if (!step1Valid || !step2Valid) {
                return;
            }

            // Show loading state
            const btn = document.getElementById('registerBtn');
            btn.querySelector('.loading').style.display = 'inline';
            btn.querySelector('.normal').style.display = 'none';
            btn.disabled = true;

            try {
                // Collect form data
                const formData = {
                    name: document.getElementById('name').value,
                    username: document.getElementById('username').value,
                    email: document.getElementById('email').value,
                    phone_number: document.getElementById('phone_number').value,
                    address: document.getElementById('address').value,
                    password: document.getElementById('password').value,
                    specialization: document.getElementById('specialization').value,
                    description: document.getElementById('description').value,
                    hourly_rate: parseFloat(document.getElementById('hourly_rate').value),
                    role: document.getElementById('role').value
                };

                // Law firm name lookup
                const lawFirmName = document.getElementById('law_firm_id').value.trim();
                let lawFirmId = null;
                if (lawFirmName) {
                    try {
                        const res = await fetch(`/api/law-firm/by-name/${encodeURIComponent(lawFirmName)}`);
                        const data = await res.json();
                        if (!res.ok || !data.id) {
                            showError('Law firm not found. Please check the name.');
                            btn.querySelector('.loading').style.display = 'none';
                            btn.querySelector('.normal').style.display = 'inline';
                            btn.disabled = false;
                            return;
                        }
                        lawFirmId = data.id;
                    } catch (err) {
                        showError('Error looking up law firm. Please try again.');
                        btn.querySelector('.loading').style.display = 'none';
                        btn.querySelector('.normal').style.display = 'inline';
                        btn.disabled = false;
                        return;
                    }
                }
                formData.law_firm_id = lawFirmId;

                // Create checkout session
                const response = await fetch('/create-attorney-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        plan: selectedPlan,
                        registration_data: formData
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    // Redirect to Stripe checkout
                    window.location.href = result.checkout_url;
                } else {
                    showError(result.error || 'Failed to create checkout session');
                }
            } catch (error) {
                showError('An error occurred. Please try again.');
                console.error('Error:', error);
            } finally {
                // Reset button state
                btn.querySelector('.loading').style.display = 'none';
                btn.querySelector('.normal').style.display = 'inline';
                btn.disabled = false;
            }
        });

        // Message functions
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        function clearMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Clear messages when user starts typing
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.addEventListener('input', function() {
                document.getElementById('errorMessage').style.display = 'none';
                document.getElementById('successMessage').style.display = 'none';
            });
        });
    </script>
</body>
</html>
