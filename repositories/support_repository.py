from case_management.models import SupportTicket, Feedback, Notification
from case_management.extensions import db, mail
from repositories.notification_repository import NotificationRepository
from flask_mail import Message
from datetime import datetime

class SupportRepository:
    @staticmethod
    def create_ticket(user_id, user_type, name, email, firm_name, priority, description, law_firm_id):
        ticket = SupportTicket(
            user_id=user_id,
            user_type=user_type,
            name=name,
            email=email,
            firm_name=firm_name,
            priority=priority,
            description=description,
            law_firm_id=law_firm_id,
            status='Open'
        )
        db.session.add(ticket)
        db.session.commit()
        
        # Create notification
        NotificationRepository.create_notification(
            user_id=user_id,
            user_type=user_type,
            message=f"Your support ticket #{ticket.id} has been created",
            notification_type='support_ticket',
            related_id=ticket.id
        )
        
        # Send email notification
        SupportRepository.send_ticket_email(ticket, 'created')
        
        return ticket

    @staticmethod
    def update_ticket_status(ticket_id, new_status):
        ticket = db.session.get(SupportTicket, ticket_id)
        if not ticket:
            return None
            
        ticket.status = new_status
        ticket.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Create notification
        NotificationRepository.create_notification(
            user_id=ticket.user_id,
            user_type=ticket.user_type,
            message=f"Your support ticket #{ticket.id} has been marked as {new_status}",
            notification_type='support_ticket',
            related_id=ticket.id
        )
        
        # Send email notification if resolved
        if new_status == 'Resolved':
            SupportRepository.send_ticket_email(ticket, 'resolved')
        
        return ticket

    @staticmethod
    def get_user_tickets(user_id, user_type):
        # Use database ID for queries
        return SupportTicket.query.filter_by(
            user_id=user_id,
            user_type=user_type
        ).order_by(SupportTicket.created_at.desc()).all()

    @staticmethod
    def create_feedback(user_id, user_type, feedback_type, rating, comment, ticket_id=None, law_firm_id=None):
        feedback = Feedback(
            user_id=user_id,
            user_type=user_type,
            ticket_id=ticket_id,
            feedback_type=feedback_type,
            rating=rating,
            comment=comment,
            law_firm_id=law_firm_id
        )
        db.session.add(feedback)
        db.session.commit()
        return feedback

    @staticmethod
    def send_ticket_email(ticket, action):
        if action == 'created':
            subject = f"Support Ticket #{ticket.id} Created"
            body = f"""
            Your support ticket has been successfully created.
            
            Ticket ID: #{ticket.id}
            Priority: {ticket.priority}
            Description: {ticket.description[:100]}...
            
            We'll get back to you soon.
            """
        else:  # resolved
            subject = f"Support Ticket #{ticket.id} Resolved"
            body = f"""
            Your support ticket has been marked as resolved.
            
            Ticket ID: #{ticket.id}
            Priority: {ticket.priority}
            
            Please provide feedback on your support experience.
            """
        
        msg = Message(
            subject=subject,
            recipients=[ticket.email],
            body=body
        )
        mail.send(msg)