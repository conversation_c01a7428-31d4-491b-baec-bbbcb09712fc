# repositories/time_entry_repository.py
from datetime import datetime
from case_management.models import <PERSON><PERSON><PERSON><PERSON>, Attorney, Employee
from case_management.extensions import db

class TimeEntryRepository:
    
    @staticmethod
    def create_time_entry(entry):
        db.session.add(entry)
        db.session.commit()
        return entry

    @staticmethod
    def get_attorney_by_id(attorney_id):
        return db.session.get(Attorney, attorney_id)
    
    @staticmethod
    def get_employee_by_id(employee_id):
        return db.session.get(Employee, employee_id)

    @staticmethod
    def get_entries(case_id=None, date=None):
        query = TimeEntry.query
        if case_id:
            query = query.filter(TimeEntry.case_id == case_id)
        if date:
            query = query.filter(TimeEntry.date == date)
        return query.all()

    @staticmethod
    def get_all_attorneys():
        return Attorney.query.all()

    @staticmethod
    def get_hourly_employees():
        return Employee.query.filter(Employee.is_hourly).all()
    
    
    @staticmethod
    def bulk_import_entries(data_list):
        results = []
        successful_imports = 0

        for entry in data_list:
            result = {'entry': entry}
            try:
                required = ['date', 'case_id', 'description', 'duration', 
                        'billed_by_type', 'billed_by_id', 'created_by_id', 
                        'created_by_type']
                if not all(field in entry for field in required):
                    result['error'] = 'Missing required fields'
                    result['missing'] = [f for f in required if f not in entry]
                    results.append(result)
                    continue

                # Validate date format
                try:
                    entry_date = datetime.strptime(entry['date'], '%Y-%m-%d').date()
                except ValueError:
                    result['error'] = 'Invalid date format (use YYYY-MM-DD)'
                    results.append(result)
                    continue

                # Validate duration
                try:
                    duration = float(entry['duration'])
                    if duration <= 0:
                        result['error'] = 'Duration must be positive'
                        results.append(result)
                        continue
                except ValueError:
                    result['error'] = 'Invalid duration format'
                    results.append(result)
                    continue

                new_entry = TimeEntry(
                    date=entry_date,
                    case_id=entry['case_id'],
                    description=entry['description'],
                    duration=duration,
                    billing_rate=entry.get('billing_rate', 0),
                    is_billable=entry.get('is_billable', True),
                    tags=entry.get('tags'),
                    entry_method='import',
                    created_by_id=entry['created_by_id'],
                    created_by_type=entry['created_by_type']
                )

                if entry['billed_by_type'] == 'attorney':
                    if not TimeEntryRepository.get_attorney_by_id(entry['billed_by_id']):
                        result['error'] = 'Attorney not found'
                        results.append(result)
                        continue
                    new_entry.billed_by_attorney_id = entry['billed_by_id']
                elif entry['billed_by_type'] == 'employee':
                    if not TimeEntryRepository.get_employee_by_id(entry['billed_by_id']):
                        result['error'] = 'Employee not found'
                        results.append(result)
                        continue
                    new_entry.billed_by_employee_id = entry['billed_by_id']
                else:
                    result['error'] = 'Invalid billed_by_type (must be "attorney" or "employee")'
                    results.append(result)
                    continue

                db.session.add(new_entry)
                db.session.flush()
                result['success'] = True
                result['id'] = new_entry.id
                results.append(result)
                successful_imports += 1

            except Exception as e:
                db.session.rollback()
                result['error'] = str(e)
                results.append(result)
                continue

        if successful_imports > 0:
            db.session.commit()
        else:
            db.session.rollback()

        return results

    @staticmethod
    def delete_entry(entry_id):
        entry = db.session.get(TimeEntry, entry_id)
        if not entry:
            return None

        db.session.delete(entry)
        db.session.commit()
        return entry
    
    @staticmethod
    def get_entry_by_id(entry_id):
        return db.session.get(TimeEntry, entry_id)
    
    
    @staticmethod
    def update_entry(entry_id, data, current_user):
        entry = db.session.get(TimeEntry, entry_id)
        if not entry:
            return None, 'Entry not found'

        try:
            # Validate and update fields
            entry.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
            entry.case_id = data['case_id']
            entry.description = data['description']
            entry.duration = float(data['duration'])
            entry.billing_rate = float(data['billing_rate'])
            entry.is_billable = data.get('is_billable', True)
            entry.tags = data.get('tags')
            entry.updated_by_id = current_user['id']
            entry.updated_by_type = current_user['type']

            if data['billed_by_type'] == 'attorney':
                entry.billed_by_attorney_id = data['billed_by_id']
                entry.billed_by_employee_id = None
            else:
                entry.billed_by_employee_id = data['billed_by_id']
                entry.billed_by_attorney_id = None

            db.session.commit()
            return entry, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)