from case_management.models import CRMLead
from case_management.extensions import db
from sqlalchemy.exc import SQLAlchemyError

class CRMLeadRepository:
    """Repository for managing CRMLead data."""

    @staticmethod
    def create_lead(data):
        try:
            lead = CRMLead(**data)
            db.session.add(lead)
            db.session.commit()
            return lead
        except SQLAlchemyError as e:
            db.session.rollback()
            print(f"Error creating lead: {e}")
            raise

    @staticmethod
    def get_all_leads():
        return CRMLead.query.order_by(CRMLead.date_submitted.desc()).all()

    @staticmethod
    def get_lead_by_id(lead_id):
        return db.session.get(CRMLead, lead_id)

    @staticmethod
    def assign_lead(lead_id, username):
        lead = db.session.get(CRMLead, lead_id)
        if lead:
            lead.assigned_to = username
            db.session.commit()
            return lead
        return None

    @staticmethod
    def update_status(lead_id, status):
        lead = db.session.get(CRMLead, lead_id)
        if lead:
            lead.status = status
            db.session.commit()
            return lead
        return None

    @staticmethod
    def delete_lead(lead_id):
        lead = db.session.get(CRMLead, lead_id)
        if lead:
            db.session.delete(lead)
            db.session.commit()
            return True
        return False
