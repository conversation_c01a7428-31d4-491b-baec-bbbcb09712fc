from case_management.extensions import db
from case_management.models import CaseDeadline, Case
from datetime import datetime
from flask import session

class CaseDeadlineRepository:
    
    @staticmethod
    def get_deadlines_by_case_id(case_id):
        return CaseDeadline.query.filter_by(case_id=case_id).all() or []
    
    
    @staticmethod
    def add_deadline(case_id, deadline_date, deadline_type, notes=None):
        deadline = CaseDeadline(
            case_id=case_id,
            deadline_date=deadline_date,
            deadline_type=deadline_type,
            notes=notes
        )
        db.session.add(deadline)
        db.session.commit()
        return deadline

    @staticmethod
    def update_deadline(deadline_id, deadline_date, deadline_type, notes=None):
        deadline = db.session.get(CaseDeadline, deadline_id)
        if not deadline:
            return None
        if deadline_date is not None:
            deadline.deadline_date = deadline_date
        if deadline_type is not None:
            deadline.deadline_type = deadline_type
        if notes is not None:
            deadline.notes = notes
        db.session.commit()
        return deadline

    @staticmethod
    def delete_deadline(deadline_id):
        deadline = db.session.get(CaseDeadline, deadline_id)
        if not deadline:
            return False
        db.session.delete(deadline)
        db.session.commit()
        return True

    @staticmethod
    def get_deadlines_for_case(case_id):
        return CaseDeadline.query.filter_by(case_id=case_id).all()
    
    @staticmethod
    def get_all_deadlines():
        return CaseDeadline.query.all()
    
    @staticmethod
    def get_deadlines_for_logged_in_attorney():
        attorney_id = session.get("attorney_id")
        user_role = session.get("user_role")

        if user_role != "attorney" or not attorney_id:
            return []

        # Find cases assigned to this attorney
        case_ids = [
            case.case_id for case in Case.query.filter_by(attorney_id=attorney_id).all()
        ]

        # Return deadlines for those cases
        return CaseDeadline.query.filter(CaseDeadline.case_id.in_(case_ids)).all()