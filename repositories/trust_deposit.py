from case_management.models import TrustDeposit
from case_management.extensions import db
import traceback

class TrustDepositRepository:
   
    @staticmethod
    def create_trust_deposit(trust_deposit_data):
        try:
            trust_deposit = TrustDeposit(**trust_deposit_data)
            db.session.add(trust_deposit)
            db.session.commit()
            return trust_deposit
        except Exception as e:
            db.session.rollback()
            traceback.print_exc()
            raise e
        
    @staticmethod
    def get_trust_deposit_by_case_id(case_id):
        return TrustDeposit.query.filter_by(case_id=case_id).first()

    @staticmethod
    def update_trust_deposit(trust_deposit_id, trust_deposit_data):
        try:
            trust_deposit = db.session.get(TrustDeposit, trust_deposit_id)
            if not trust_deposit:
                return None
            
            for key, value in trust_deposit_data.items():
                setattr(trust_deposit, key, value)
            
            db.session.commit()
            return trust_deposit
        except Exception as e:
            db.session.rollback()
            traceback.print_exc()
            raise e