from case_management.extensions import db
from case_management.models import ExternalContact
from datetime import datetime


class ExternalContactRepository:

    @staticmethod
    def get_all_contacts():
        """Retrieve all external contacts."""
        return ExternalContact.query.order_by(ExternalContact.name).all()

    @staticmethod
    def get_contact_by_id(contact_id):
        """Retrieve a contact by primary ID."""
        return ExternalContact.query.filter_by(id=contact_id).first()

    @staticmethod
    def search_contacts_by_name(name: str):
        """Search contacts using partial name match (case-insensitive)."""
        return ExternalContact.query.filter(ExternalContact.name.ilike(f"%{name}%")).all()

    @staticmethod
    def create_contact(name, contact_type=None, email=None, phone=None, role=None, case_link=None, last_contact=None):
        """Create a new external contact."""
        # Validate required fields
        if not name or not name.strip():
            print("❌ Error creating contact: Name is required and cannot be empty")
            return None
        
        # Validate name length (database limit is 255 characters)
        if len(name.strip()) > 255:
            print("❌ Error creating contact: Name is too long (max 255 characters)")
            return None
        
        try:
            new_contact = ExternalContact(
                name=name.strip(),
                contact_type=contact_type or 'External',
                email=email,
                phone=phone,
                role=role,
                case_link=case_link,
                last_contact=last_contact,
                last_login=None  # Usually None
            )
            db.session.add(new_contact)
            db.session.commit()
            return new_contact
        except Exception as e:
            print(f"❌ Error creating contact: {e}")
            db.session.rollback()
            return None

    @staticmethod
    def update_contact(contact_id, name=None, contact_type=None, email=None, phone=None, role=None, case_link=None, last_contact=None):
        """Update an external contact."""
        contact = ExternalContactRepository.get_contact_by_id(contact_id)
        if not contact:
            return None

        if name:
            contact.name = name
        if contact_type:
            contact.contact_type = contact_type
        if email:
            contact.email = email
        if phone:
            contact.phone = phone
        if role:
            contact.role = role
        if case_link:
            contact.case_link = case_link
        if last_contact:
            contact.last_contact = last_contact

        try:
            db.session.commit()
            return contact
        except Exception as e:
            print(f"❌ Error updating contact: {e}")
            db.session.rollback()
            return None

    @staticmethod
    def delete_contact(contact_id):
        """Delete an external contact by ID."""
        contact = ExternalContactRepository.get_contact_by_id(contact_id)
        if not contact:
            return False
        try:
            db.session.delete(contact)
            db.session.commit()
            return True
        except Exception as e:
            print(f"❌ Error deleting contact: {e}")
            db.session.rollback()
            return False
