from case_management.extensions import db
from case_management.models import Case, Client, CaseTask, CaseDeadline


class CaseRepository:

    @staticmethod
    def get_all_cases():
        """Retrieve all cases"""
        return Case.query.all()

    @staticmethod
    def get_all_cases_by_attorney(attorney_id):
        """Retrieve all cases by attorney ID."""
        return Case.query.filter_by(attorney_id=attorney_id).all()
    
    @staticmethod
    def create_case(case_id, client_id, client_name, case_name, case_type, case_status,
                    role, representative_id, description=None, custom_fields=None, billing_info_id=None):
        """Create a new case."""

        # Ensure custom_fields is a dictionary
        if custom_fields is None:
            custom_fields = {}

        # Prepare the case data
        case_data = {
            'case_id': case_id,
            'client_id': client_id,
            'client_name': client_name,
            'case_name': case_name,
            'case_type': case_type,
            'case_status': case_status,
            'description': description,
            'custom_fields': custom_fields,
            'billing_info_id': billing_info_id
        }

        if role == 'employee':
            case_data['employee_id'] = int(representative_id)
        elif role == 'attorney':
            case_data['attorney_id'] = representative_id

        new_case = Case(**case_data)
        db.session.add(new_case)
        db.session.commit()
        return new_case

    @staticmethod
    def get_case_by_id(case_id):
        """Retrieve a case by its ID."""
        return Case.query.filter_by(case_id=case_id).first()
    
    @staticmethod
    def get_cases_by_client_id(client_id):
        return Case.query.filter_by(client_id=client_id).all()

    @staticmethod
    def get_cases_by_client(client_id):
        """Retrieve all cases for a specific client."""
        return Case.query.filter_by(client_id=client_id).all()
    
    @staticmethod
    def get_case_by_client_and_id(client_id, case_id):
        """Fetch a case that belongs to a specific client."""
        return Case.query.filter_by(client_id=client_id, case_id=case_id).first()
    
    
    @staticmethod
    def get_case_by_client_name_and_case_name(client_name, case_name):
        """
        Retrieve a case based on the client's full name and the case's name.
        This method joins the Client and Case tables to ensure the case belongs to the given client.
        
        """
        return Case.query.join(Client, Client.client_id == Case.client_id) \
            .filter(Client.name == client_name, Case.case_name == case_name) \
            .first()

    @staticmethod
    def get_cases_by_client_name(client_name):
        """Retrieve cases by client name."""
        return Case.query.filter_by(client_name=client_name).all()

    @staticmethod
    def get_cases_by_case_name(case_name):
        """Retrieve cases by case name."""
        return Case.query.filter_by(case_name=case_name).all()

    @staticmethod
    def get_cases_by_case_type(case_type):
        """Retrieve cases by case type."""
        return Case.query.filter_by(case_type=case_type).all()

    @staticmethod
    def get_cases_by_case_status(case_status):
        """Retrieve cases by case status."""
        return Case.query.filter_by(case_status=case_status).all()
    
    @staticmethod
    def update_case(case_id, client_name=None, case_name=None, case_type=None, case_status=None, description=None, custom_fields=None):
        """Update case details, including custom fields."""
        try:
            case = CaseRepository.get_case_by_id(case_id)
            if not case:
                return None

            # Update standard fields
            if client_name:
                case.client_name = client_name
            if case_name:
                case.case_name = case_name
            if case_type:
                case.case_type = case_type
            if case_status:
                case.case_status = case_status
            if description:
                case.description = description

            # Update custom fields
            if custom_fields:
                case.custom_fields = custom_fields

            db.session.commit()
            return case

        except Exception as e:
            db.session.rollback()  # Rollback in case of error
            print(f"Error updating case: {e}")
            return None

    @staticmethod
    def update_deadline(deadline_id, deadline_date=None, deadline_type=None, notes=None):
        """Update a deadline."""
        deadline = db.session.get(CaseDeadline, deadline_id)
        if not deadline:
            return None

        if deadline_date:
            deadline.deadline_date = deadline_date
        if deadline_type:
            deadline.deadline_type = deadline_type
        if notes:
            deadline.notes = notes

        db.session.commit()
        return deadline

    @staticmethod
    def update_task(task_id, task_type=None, description=None, due_date=None, status=None):
        """Update a task."""
        task = db.session.get(CaseTask, task_id)
        if not task:
            return None

        if task_type:
            task.task_type = task_type
        if description:
            task.description = description
        if due_date:
            task.due_date = due_date
        if status:
            task.status = status

        db.session.commit()
        return task

    @staticmethod
    def delete_case(case_id):
        """Delete a case by its ID."""
        case = CaseRepository.get_case_by_id(case_id)
        if not case:
            return None

        db.session.delete(case)
        db.session.commit()
        return True

