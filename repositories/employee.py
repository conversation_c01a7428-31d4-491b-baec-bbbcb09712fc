from case_management.models import Employee
from case_management.extensions import db
import traceback

class EmployeeRepository:
   
    @staticmethod
    def get_all_employees():
        return Employee.query.all()
    
    @staticmethod
    def get_employee_by_username(username):
        return Employee.query.filter_by(username=username).first()


    @staticmethod
    def get_employees_by_law_firm(law_firm_id):
        return Employee.query.filter_by(law_firm_id=law_firm_id).all()
    
    @staticmethod
    def get_employee_by_email(email):
        """
        Get an employee by their email address.
        Returns None if no employee is found.
        """
        return Employee.query.filter_by(email=email).first()

    @staticmethod
    def get_employee_by_id(employee_id):
        return db.session.get(Employee, employee_id)
    
    @staticmethod
    def get_employee_by_db_id(id):
        return Employee.query.filter_by(id=id).first()

    @staticmethod
    def create_employee(name, email, username, password_hash, role, hourly_rate, law_firm_id, address=None, phone_number=None):
       
        try:
            # Validate required fields
            if not all([name, email, username, password_hash, role, hourly_rate, law_firm_id]):
                raise ValueError("Missing required fields to create an employee.")

            # Create a new Employee instance
            employee = Employee(
                name=name,
                email=email,
                username=username,
                password_hash=password_hash,
                role=role,
                hourly_rate=hourly_rate,
                law_firm_id=law_firm_id,
                phone_number=phone_number
        )
            db.session.add(employee)
            db.session.commit()
            print("Employee created successfully:", employee.id)
            return employee

        except ValueError as e:
            db.session.rollback()
            print(f"Validation error: {e}")
            return None

        except Exception as e:
            db.session.rollback()
            traceback.print_exc()  # This will give you the real reason
            print(f"Unexpected error during employee creation: {e}")
            return None
    
    @staticmethod
    def get_employees_by_name(name: str):
        return Employee.query.filter(Employee.name.ilike(f"%{name}%")).all()