from case_management.extensions import db
from case_management.models import CaseTask

class CaseTaskRepository:
    @staticmethod
    def add_task(
        case_id,
        task_type,
        description=None,
        due_date=None,
        status='pending',
        priority='medium',
        assigned_to_employee_id=None,
        assigned_to_client_id=None
    ):
        """
        Creates a new task with the provided parameters.
        Maintains all existing logic while adding new fields.
        """
        task = CaseTask(
            case_id=case_id,
            task_type=task_type,
            description=description,
            due_date=due_date,
            status=status,
            priority=priority,
            assigned_to_employee_id=assigned_to_employee_id,
            assigned_to_client_id=assigned_to_client_id
        )
        db.session.add(task)
        db.session.commit()
        return task
    
    @staticmethod
    def get_tasks_by_case_id(case_id):
        """Returns all tasks for a specific case (unchanged)"""
        return CaseTask.query.filter_by(case_id=case_id).all() or []

    @staticmethod
    def update_task(
        task_id,
        task_type=None,
        description=None,
        due_date=None,
        status=None,
        priority=None,
        assigned_to_employee_id=None,
        assigned_to_client_id=None
    ):
        """
        Updates a task with the provided parameters.
        Maintains all existing logic while adding new fields.
        """
        task = db.session.get(CaseTask, task_id)
        if not task:
            return None
            
        # Existing fields
        if task_type is not None:
            task.task_type = task_type
        if description is not None:
            task.description = description
        if due_date is not None:
            task.due_date = due_date
        if status is not None:
            task.status = status
            
        # New fields
        if priority is not None:
            task.priority = priority
        if assigned_to_employee_id is not None:
            task.assigned_to_employee_id = assigned_to_employee_id
            task.assigned_to_client_id = None  # Clear client assignment if assigning to employee
        if assigned_to_client_id is not None:
            task.assigned_to_client_id = assigned_to_client_id
            task.assigned_to_employee_id = None  # Clear employee assignment if assigning to client
            
        db.session.commit()
        return task

    @staticmethod
    def delete_task(task_id):
        """Deletes a task (unchanged)"""
        task = db.session.get(CaseTask, task_id)
        if not task:
            return False
        db.session.delete(task)
        db.session.commit()
        return True

    @staticmethod
    def get_tasks_for_case(case_id):
        """Returns all tasks for a specific case (unchanged)"""
        return CaseTask.query.filter_by(case_id=case_id).all()
    
    @staticmethod
    def get_all_tasks():
        """Returns all tasks (unchanged)"""
        return CaseTask.query.all()